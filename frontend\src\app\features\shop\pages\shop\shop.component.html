<div class="shop-container">
  <!-- Global Error Display -->
  <app-error-display [showGlobalErrors]="true"></app-error-display>

  <!-- Loading State -->
  <app-loading-spinner
    *ngIf="loading"
    [showGlobalLoading]="true"
    message="Loading shop data..."
    webSpinnerType="pulse">
  </app-loading-spinner>

  <!-- Shop Content -->
  <div *ngIf="!loading" class="shop-content">
    <!-- Global Search Bar -->
    <div class="search-section">
      <div class="search-bar">
        <input 
          type="text" 
          [(ngModel)]="searchQuery" 
          placeholder="Search products, brands, categories..."
          (keyup.enter)="search()"
          class="search-input">
        <button (click)="search()" class="search-btn">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>

    <!-- Shop by Category Section -->
    <section class="category-section">
      <h2 class="section-title">Shop by Category</h2>
      <div class="categories-grid">
        <div 
          *ngFor="let category of categories" 
          class="category-card"
          (click)="navigateToCategory(category)">
          <div class="category-icon">{{ category.icon }}</div>
          <h3 class="category-name">{{ category.name }}</h3>
        </div>
      </div>
    </section>

    <!-- Featured Brands Section -->
    <section class="featured-brands-section">
      <h2 class="section-title">Featured Brands</h2>

      <!-- Loading State for Brands -->
      <app-loading-spinner
        [showInlineLoading]="true"
        [loadingKey]="'shop.brands.load'"
        message="Loading brands..."
        size="small"
        webSpinnerType="dots">
      </app-loading-spinner>

      <!-- Brands Slider -->
      <div *ngIf="featuredBrands.length > 0" class="brands-slider">
        <div *ngFor="let brand of featuredBrands" class="brand-card">
          <img [src]="brand.logo" [alt]="brand.name" class="brand-logo">
          <h3 class="brand-name">{{ brand.name }}</h3>
          <span *ngIf="brand.isPopular" class="popular-badge">Popular</span>
        </div>
      </div>

      <!-- No Data State -->
      <app-no-data
        *ngIf="featuredBrands.length === 0 && !loading"
        title="No Featured Brands"
        message="We're working on adding featured brands. Check back soon!"
        iconClass="fas fa-store"
        containerClass="compact"
        [showActions]="true"
        primaryAction="Browse All Brands"
        [suggestions]="['Nike', 'Adidas', 'Zara', 'H&M', 'Uniqlo']"
        suggestionsTitle="Popular Brands:">
      </app-no-data>
    </section>

    <!-- Trending Now Section -->
    <section class="trending-section">
      <h2 class="section-title">Trending Now</h2>

      <!-- Products Grid -->
      <div *ngIf="trendingProducts.length > 0" class="products-grid">
        <div *ngFor="let product of trendingProducts" class="product-card" (click)="viewProduct(product)">
          <div class="product-image-container">
            <img [src]="getProductImage(product)" [alt]="product.name" class="product-image">
            <div *ngIf="getDiscountPercentage(product) > 0" class="discount-badge">
              {{ getDiscountPercentage(product) }}% OFF
            </div>
            <div class="product-actions">
              <button (click)="likeProduct(product, $event)" class="action-btn like-btn" 
                      [class.liked]="product.isLiked">
                <i class="fas fa-heart"></i>
                <span *ngIf="product.likesCount">{{ product.likesCount }}</span>
              </button>
              <button (click)="shareProduct(product, $event)" class="action-btn share-btn">
                <i class="fas fa-share"></i>
                <span *ngIf="product.sharesCount">{{ product.sharesCount }}</span>
              </button>
              <button (click)="commentOnProduct(product, $event)" class="action-btn comment-btn">
                <i class="fas fa-comment"></i>
                <span *ngIf="product.commentsCount">{{ product.commentsCount }}</span>
              </button>
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-brand">{{ product.brand }}</p>
            <div class="product-pricing">
              <span class="current-price">₹{{ product.price }}</span>
              <span *ngIf="product.originalPrice && product.originalPrice > product.price" 
                    class="original-price">₹{{ product.originalPrice }}</span>
            </div>
            <div class="product-buttons">
              <button (click)="addToWishlist(product, $event)" class="btn-wishlist">
                <i class="fas fa-heart"></i> Wishlist
              </button>
              <button (click)="addToCart(product, $event)" class="btn-cart">
                <i class="fas fa-shopping-cart"></i> Add to Cart
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- No Data State -->
      <app-no-data
        *ngIf="trendingProducts.length === 0 && !loading"
        title="No Trending Products"
        message="Discover what's hot! We're updating our trending collection."
        iconClass="fas fa-fire"
        containerClass="compact"
        [showActions]="true"
        primaryAction="Browse All Products"
        secondaryAction="View Categories"
        [suggestions]="['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Accessories']"
        suggestionsTitle="Popular Categories:">
      </app-no-data>
    </section>

    <!-- New Arrivals Section -->
    <section class="new-arrivals-section">
      <h2 class="section-title">New Arrivals</h2>

      <!-- Products Grid -->
      <div *ngIf="newArrivals.length > 0" class="products-grid">
        <div *ngFor="let product of newArrivals" class="product-card" (click)="viewProduct(product)">
          <div class="product-image-container">
            <img [src]="getProductImage(product)" [alt]="product.name" class="product-image">
            <div class="new-badge">NEW</div>
            <div class="product-actions">
              <button (click)="likeProduct(product, $event)" class="action-btn like-btn" 
                      [class.liked]="product.isLiked">
                <i class="fas fa-heart"></i>
                <span *ngIf="product.likesCount">{{ product.likesCount }}</span>
              </button>
              <button (click)="shareProduct(product, $event)" class="action-btn share-btn">
                <i class="fas fa-share"></i>
                <span *ngIf="product.sharesCount">{{ product.sharesCount }}</span>
              </button>
              <button (click)="commentOnProduct(product, $event)" class="action-btn comment-btn">
                <i class="fas fa-comment"></i>
                <span *ngIf="product.commentsCount">{{ product.commentsCount }}</span>
              </button>
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-brand">{{ product.brand }}</p>
            <div class="product-pricing">
              <span class="current-price">₹{{ product.price }}</span>
              <span *ngIf="product.originalPrice && product.originalPrice > product.price" 
                    class="original-price">₹{{ product.originalPrice }}</span>
            </div>
            <div class="product-buttons">
              <button (click)="addToWishlist(product, $event)" class="btn-wishlist">
                <i class="fas fa-heart"></i> Wishlist
              </button>
              <button (click)="addToCart(product, $event)" class="btn-cart">
                <i class="fas fa-shopping-cart"></i> Add to Cart
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- No Data State -->
      <app-no-data
        *ngIf="newArrivals.length === 0 && !loading"
        title="No New Arrivals"
        message="Stay tuned for the latest fashion arrivals!"
        iconClass="fas fa-sparkles"
        containerClass="compact"
        [showActions]="true"
        primaryAction="Browse All Products"
        secondaryAction="Set Alerts"
        [suggestions]="['Summer Collection', 'Winter Wear', 'Casual Outfits', 'Formal Wear']"
        suggestionsTitle="Coming Soon:">
      </app-no-data>
    </section>
  </div>
</div>
