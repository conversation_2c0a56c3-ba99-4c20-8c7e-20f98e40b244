import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { Observable, map } from 'rxjs';

export interface RolePermissions {
  [key: string]: {
    read: boolean;
    write: boolean;
    delete: boolean;
    admin: boolean;
  };
}

export interface UserRole {
  name: string;
  level: number;
  permissions: RolePermissions;
}

@Injectable({
  providedIn: 'root'
})
export class RoleAccessService {

  private roleHierarchy: { [key: string]: UserRole } = {
    'buyer': {
      name: 'Buyer',
      level: 1,
      permissions: {
        'profile': { read: true, write: true, delete: false, admin: false },
        'orders': { read: true, write: true, delete: false, admin: false },
        'cart': { read: true, write: true, delete: true, admin: false },
        'wishlist': { read: true, write: true, delete: true, admin: false },
        'reviews': { read: true, write: true, delete: true, admin: false },
        'posts': { read: true, write: false, delete: false, admin: false },
        'stories': { read: true, write: false, delete: false, admin: false },
        'products': { read: true, write: false, delete: false, admin: false },
        'analytics': { read: false, write: false, delete: false, admin: false },
        'users': { read: false, write: false, delete: false, admin: false },
        'settings': { read: true, write: true, delete: false, admin: false }
      }
    },
    'seller': {
      name: 'Seller',
      level: 2,
      permissions: {
        'profile': { read: true, write: true, delete: false, admin: false },
        'orders': { read: true, write: true, delete: false, admin: false },
        'cart': { read: true, write: true, delete: true, admin: false },
        'wishlist': { read: true, write: true, delete: true, admin: false },
        'reviews': { read: true, write: true, delete: true, admin: false },
        'posts': { read: true, write: true, delete: true, admin: false },
        'stories': { read: true, write: true, delete: true, admin: false },
        'products': { read: true, write: true, delete: true, admin: false },
        'analytics': { read: true, write: false, delete: false, admin: false },
        'users': { read: false, write: false, delete: false, admin: false },
        'settings': { read: true, write: true, delete: false, admin: false },
        'vendor': { read: true, write: true, delete: false, admin: false }
      }
    },
    'admin': {
      name: 'Admin',
      level: 3,
      permissions: {
        'profile': { read: true, write: true, delete: true, admin: true },
        'orders': { read: true, write: true, delete: true, admin: true },
        'cart': { read: true, write: true, delete: true, admin: true },
        'wishlist': { read: true, write: true, delete: true, admin: true },
        'reviews': { read: true, write: true, delete: true, admin: true },
        'posts': { read: true, write: true, delete: true, admin: true },
        'stories': { read: true, write: true, delete: true, admin: true },
        'products': { read: true, write: true, delete: true, admin: true },
        'analytics': { read: true, write: true, delete: false, admin: true },
        'users': { read: true, write: true, delete: true, admin: true },
        'settings': { read: true, write: true, delete: true, admin: true },
        'vendor': { read: true, write: true, delete: true, admin: true },
        'categories': { read: true, write: true, delete: true, admin: true },
        'brands': { read: true, write: true, delete: true, admin: true }
      }
    },
    'super_admin': {
      name: 'Super Admin',
      level: 4,
      permissions: {
        'profile': { read: true, write: true, delete: true, admin: true },
        'orders': { read: true, write: true, delete: true, admin: true },
        'cart': { read: true, write: true, delete: true, admin: true },
        'wishlist': { read: true, write: true, delete: true, admin: true },
        'reviews': { read: true, write: true, delete: true, admin: true },
        'posts': { read: true, write: true, delete: true, admin: true },
        'stories': { read: true, write: true, delete: true, admin: true },
        'products': { read: true, write: true, delete: true, admin: true },
        'analytics': { read: true, write: true, delete: true, admin: true },
        'users': { read: true, write: true, delete: true, admin: true },
        'settings': { read: true, write: true, delete: true, admin: true },
        'vendor': { read: true, write: true, delete: true, admin: true },
        'categories': { read: true, write: true, delete: true, admin: true },
        'brands': { read: true, write: true, delete: true, admin: true },
        'system': { read: true, write: true, delete: true, admin: true }
      }
    }
  };

  constructor(private authService: AuthService) {}

  // Get current user role
  getCurrentUserRole(): string {
    const user = this.authService.getCurrentUser();
    return user?.role || 'buyer';
  }

  // Get role information
  getRoleInfo(role?: string): UserRole | null {
    const userRole = role || this.getCurrentUserRole();
    return this.roleHierarchy[userRole] || null;
  }

  // Check if user has permission for a specific action
  hasPermission(module: string, action: 'read' | 'write' | 'delete' | 'admin'): boolean {
    const roleInfo = this.getRoleInfo();
    if (!roleInfo) return false;

    const modulePermissions = roleInfo.permissions[module];
    if (!modulePermissions) return false;

    return modulePermissions[action];
  }

  // Check if user can read a module
  canRead(module: string): boolean {
    return this.hasPermission(module, 'read');
  }

  // Check if user can write to a module
  canWrite(module: string): boolean {
    return this.hasPermission(module, 'write');
  }

  // Check if user can delete from a module
  canDelete(module: string): boolean {
    return this.hasPermission(module, 'delete');
  }

  // Check if user has admin access to a module
  hasAdminAccess(module: string): boolean {
    return this.hasPermission(module, 'admin');
  }

  // Check if user role is at least a certain level
  hasRoleLevel(minimumLevel: number): boolean {
    const roleInfo = this.getRoleInfo();
    return roleInfo ? roleInfo.level >= minimumLevel : false;
  }

  // Check if user is buyer
  isBuyer(): boolean {
    return this.getCurrentUserRole() === 'buyer';
  }

  // Check if user is seller
  isSeller(): boolean {
    return this.getCurrentUserRole() === 'seller';
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.getCurrentUserRole() === 'admin';
  }

  // Check if user is super admin
  isSuperAdmin(): boolean {
    return this.getCurrentUserRole() === 'super_admin';
  }

  // Check if user has elevated privileges (admin or super admin)
  hasElevatedPrivileges(): boolean {
    return this.isAdmin() || this.isSuperAdmin();
  }

  // Get available modules for current user
  getAvailableModules(): string[] {
    const roleInfo = this.getRoleInfo();
    if (!roleInfo) return [];

    return Object.keys(roleInfo.permissions).filter(module => 
      roleInfo.permissions[module].read
    );
  }

  // Get editable modules for current user
  getEditableModules(): string[] {
    const roleInfo = this.getRoleInfo();
    if (!roleInfo) return [];

    return Object.keys(roleInfo.permissions).filter(module => 
      roleInfo.permissions[module].write
    );
  }

  // Observable methods for reactive programming
  canRead$(module: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(() => this.canRead(module))
    );
  }

  canWrite$(module: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(() => this.canWrite(module))
    );
  }

  canDelete$(module: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(() => this.canDelete(module))
    );
  }

  hasAdminAccess$(module: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(() => this.hasAdminAccess(module))
    );
  }

  // Get filtered navigation items based on role
  getFilteredNavigation(navigationItems: any[]): any[] {
    return navigationItems.filter(item => {
      if (item.requiredRole) {
        return this.hasRoleLevel(this.roleHierarchy[item.requiredRole]?.level || 0);
      }
      if (item.requiredPermission) {
        const [module, action] = item.requiredPermission.split(':');
        return this.hasPermission(module, action as any);
      }
      return true;
    });
  }

  // Get role-specific settings visibility
  getSettingsVisibility(): { [key: string]: boolean } {
    const role = this.getCurrentUserRole();
    
    const baseSettings = {
      'profile': true,
      'notifications': true,
      'privacy': true,
      'security': true
    };

    const roleSpecificSettings: { [key: string]: { [key: string]: boolean } } = {
      'buyer': {
        ...baseSettings,
        'orders': true,
        'addresses': true,
        'payment': true
      },
      'seller': {
        ...baseSettings,
        'vendor': true,
        'products': true,
        'analytics': true,
        'orders': true,
        'payments': true
      },
      'admin': {
        ...baseSettings,
        'users': true,
        'products': true,
        'orders': true,
        'analytics': true,
        'system': true,
        'vendor': true
      },
      'super_admin': {
        ...baseSettings,
        'users': true,
        'products': true,
        'orders': true,
        'analytics': true,
        'system': true,
        'vendor': true,
        'database': true,
        'logs': true
      }
    };

    return roleSpecificSettings[role] || baseSettings;
  }
}
