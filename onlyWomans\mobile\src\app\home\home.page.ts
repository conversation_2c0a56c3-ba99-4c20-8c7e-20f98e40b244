import { Component, OnInit, ViewChild } from '@angular/core';
import { IonContent, IonInfiniteScroll } from '@ionic/angular';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})
export class HomePage implements OnInit {
  @ViewChild(IonContent, { static: false }) content!: IonContent;
  @ViewChild(IonInfiniteScroll, { static: false }) infiniteScroll!: IonInfiniteScroll;

  // Hero slider data
  heroSlides = [
    {
      id: 1,
      title: 'New Collection',
      subtitle: 'Ethnic Elegance',
      description: 'Discover traditional wear with modern touch',
      image: 'assets/images/hero/ethnic-collection.jpg',
      buttonText: 'Shop Ethnic',
      buttonLink: '/ethnic',
      gradient: 'linear-gradient(135deg, #ec4899, #a855f7)'
    },
    {
      id: 2,
      title: 'Western Vibes',
      subtitle: 'Contemporary Style',
      description: 'Trendy outfits for the modern woman',
      image: 'assets/images/hero/western-collection.jpg',
      buttonText: 'Shop Western',
      buttonLink: '/western',
      gradient: 'linear-gradient(135deg, #f59e0b, #ec4899)'
    },
    {
      id: 3,
      title: 'Beauty Essentials',
      subtitle: 'Glow Up',
      description: 'Premium makeup and skincare products',
      image: 'assets/images/hero/beauty-collection.jpg',
      buttonText: 'Shop Beauty',
      buttonLink: '/beauty',
      gradient: 'linear-gradient(135deg, #a855f7, #3b82f6)'
    }
  ];

  // Quick categories
  quickCategories = [
    { name: 'Sarees', icon: '🥻', link: '/categories/sarees', color: '#ec4899' },
    { name: 'Dresses', icon: '👗', link: '/categories/dresses', color: '#a855f7' },
    { name: 'Makeup', icon: '💄', link: '/categories/makeup', color: '#f59e0b' },
    { name: 'Jewelry', icon: '💎', link: '/categories/jewelry', color: '#10b981' },
    { name: 'Bags', icon: '👜', link: '/categories/bags', color: '#3b82f6' },
    { name: 'Shoes', icon: '👠', link: '/categories/shoes', color: '#ef4444' },
    { name: 'Lingerie', icon: '🩱', link: '/categories/lingerie', color: '#8b5cf6' },
    { name: 'Skincare', icon: '🧴', link: '/categories/skincare', color: '#06b6d4' }
  ];

  // Featured products
  featuredProducts = [
    {
      id: 1,
      name: 'Floral Maxi Dress',
      brand: 'StyleVogue',
      price: 2499,
      originalPrice: 3499,
      discount: 29,
      image: 'assets/images/products/dress1.jpg',
      rating: 4.5,
      reviewCount: 128,
      isNew: true,
      isWishlisted: false,
      colors: ['#ff6b9d', '#4ecdc4', '#45b7d1']
    },
    {
      id: 2,
      name: 'Silk Saree with Blouse',
      brand: 'EthnicElegance',
      price: 4999,
      originalPrice: 6999,
      discount: 29,
      image: 'assets/images/products/saree1.jpg',
      rating: 4.8,
      reviewCount: 95,
      isNew: false,
      isWishlisted: true,
      colors: ['#ff6b9d', '#ffd93d', '#6bcf7f']
    },
    {
      id: 3,
      name: 'Designer Handbag',
      brand: 'LuxeCollection',
      price: 1899,
      originalPrice: null,
      discount: null,
      image: 'assets/images/products/bag1.jpg',
      rating: 4.3,
      reviewCount: 67,
      isNew: true,
      isWishlisted: false,
      colors: ['#8b4513', '#000000', '#ff6b9d']
    },
    {
      id: 4,
      name: 'Makeup Palette Set',
      brand: 'GlamBeauty',
      price: 1299,
      originalPrice: 1799,
      discount: 28,
      image: 'assets/images/products/makeup1.jpg',
      rating: 4.6,
      reviewCount: 203,
      isNew: false,
      isWishlisted: false,
      colors: ['#ff6b9d', '#ffd93d', '#8b5cf6']
    }
  ];

  // Special offers
  specialOffers = [
    {
      id: 1,
      title: 'First Order',
      subtitle: '30% OFF',
      description: 'Use code: WELCOME30',
      icon: '🎁',
      color: '#ec4899',
      action: 'Claim Now'
    },
    {
      id: 2,
      title: 'Free Shipping',
      subtitle: 'On ₹999+',
      description: 'No minimum for premium',
      icon: '🚚',
      color: '#10b981',
      action: 'Shop Now'
    }
  ];

  // Component state
  isLoading = false;
  currentSlide = 0;

  constructor() {}

  ngOnInit() {
    this.loadInitialData();
  }

  loadInitialData() {
    // Simulate API call
    this.isLoading = true;
    setTimeout(() => {
      this.isLoading = false;
    }, 1000);
  }

  onSlideChange(event: any) {
    this.currentSlide = event.detail[0].activeIndex;
  }

  toggleWishlist(product: any) {
    product.isWishlisted = !product.isWishlisted;
    // Add haptic feedback
    // Haptics.impact({ style: ImpactStyle.Light });
  }

  addToCart(product: any) {
    // Add to cart logic
    console.log('Adding to cart:', product);
    // Add haptic feedback
    // Haptics.impact({ style: ImpactStyle.Medium });
  }

  loadMoreProducts(event: any) {
    setTimeout(() => {
      // Load more products
      console.log('Loading more products...');
      event.target.complete();
    }, 1000);
  }

  doRefresh(event: any) {
    setTimeout(() => {
      this.loadInitialData();
      event.target.complete();
    }, 1000);
  }

  scrollToTop() {
    this.content.scrollToTop(500);
  }

  getStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push('★');
    }
    
    if (hasHalfStar) {
      stars.push('☆');
    }
    
    while (stars.length < 5) {
      stars.push('☆');
    }
    
    return stars;
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(price);
  }
}
