import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { ErrorHandlerService, AppError } from '../../../core/services/error-handler.service';

@Component({
  selector: 'app-error-display',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- Global Error Banner -->
    <div *ngIf="showGlobalErrors && globalErrors.length > 0" class="error-banner">
      <div *ngFor="let error of globalErrors" class="error-item" [class]="getErrorClass(error)">
        <div class="error-content">
          <div class="error-icon">
            <i [class]="getErrorIcon(error)"></i>
          </div>
          
          <div class="error-details">
            <h4 class="error-title">{{ getErrorTitle(error) }}</h4>
            <p class="error-message">{{ error.message }}</p>
            
            <div *ngIf="showDetails && error.details" class="error-extra">
              <button class="toggle-details" (click)="toggleDetails(error.id)">
                <i class="fas" [class.fa-chevron-down]="!expandedErrors[error.id]" 
                   [class.fa-chevron-up]="expandedErrors[error.id]"></i>
                {{ expandedErrors[error.id] ? 'Hide' : 'Show' }} Details
              </button>
              
              <div *ngIf="expandedErrors[error.id]" class="error-details-content">
                <pre>{{ error.details | json }}</pre>
              </div>
            </div>
          </div>
          
          <div class="error-actions">
            <button *ngIf="error.retryable" 
                    class="btn-retry" 
                    (click)="onRetry.emit(error)">
              <i class="fas fa-redo"></i>
              Retry
            </button>
            
            <button class="btn-dismiss" (click)="dismissError(error.id)">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        
        <div *ngIf="error.context" class="error-context">
          <small>Context: {{ error.context }}</small>
        </div>
      </div>
    </div>

    <!-- Inline Error Display -->
    <div *ngIf="inlineError" class="inline-error" [class]="getErrorClass(inlineError)">
      <div class="error-content">
        <div class="error-icon">
          <i [class]="getErrorIcon(inlineError)"></i>
        </div>
        
        <div class="error-details">
          <p class="error-message">{{ inlineError.message }}</p>
        </div>
        
        <div class="error-actions">
          <button *ngIf="inlineError.retryable" 
                  class="btn-retry" 
                  (click)="onRetry.emit(inlineError)">
            <i class="fas fa-redo"></i>
            Retry
          </button>
          
          <button *ngIf="dismissible" 
                  class="btn-dismiss" 
                  (click)="onDismiss.emit()">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Error Toast -->
    <div *ngIf="showToast && toastError" 
         class="error-toast" 
         [class]="getErrorClass(toastError)"
         [@slideIn]>
      <div class="toast-content">
        <div class="toast-icon">
          <i [class]="getErrorIcon(toastError)"></i>
        </div>
        
        <div class="toast-message">
          {{ toastError.message }}
        </div>
        
        <button class="toast-close" (click)="hideToast()">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .error-banner {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: #dc3545;
      color: white;
      padding: 1rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .error-item {
      margin-bottom: 1rem;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.network {
        background: #fd7e14;
      }
      
      &.validation {
        background: #ffc107;
        color: #212529;
      }
      
      &.authentication {
        background: #dc3545;
      }
      
      &.authorization {
        background: #6f42c1;
      }
    }

    .error-content {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
    }

    .error-icon {
      font-size: 1.25rem;
      margin-top: 0.25rem;
    }

    .error-details {
      flex: 1;
    }

    .error-title {
      font-size: 1rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    .error-message {
      margin: 0 0 0.5rem 0;
      line-height: 1.4;
    }

    .error-extra {
      margin-top: 0.5rem;
    }

    .toggle-details {
      background: none;
      border: none;
      color: inherit;
      cursor: pointer;
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;
      
      &:hover {
        text-decoration: underline;
      }
    }

    .error-details-content {
      margin-top: 0.5rem;
      padding: 0.5rem;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      font-size: 0.75rem;
      max-height: 200px;
      overflow-y: auto;
      
      pre {
        margin: 0;
        white-space: pre-wrap;
      }
    }

    .error-actions {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .btn-retry, .btn-dismiss {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: inherit;
      padding: 0.5rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .error-context {
      margin-top: 0.5rem;
      opacity: 0.8;
      font-size: 0.75rem;
    }

    .inline-error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
      border-radius: 8px;
      padding: 1rem;
      margin: 1rem 0;
      
      &.network {
        background: #ffeaa7;
        color: #856404;
        border-color: #ffd93d;
      }
      
      &.validation {
        background: #fff3cd;
        color: #856404;
        border-color: #ffeaa7;
      }
      
      &.authentication {
        background: #f8d7da;
        color: #721c24;
        border-color: #f5c6cb;
      }
    }

    .error-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1100;
      background: #dc3545;
      color: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      max-width: 400px;
      animation: slideIn 0.3s ease;
    }

    .toast-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
    }

    .toast-icon {
      font-size: 1.25rem;
    }

    .toast-message {
      flex: 1;
      font-size: 0.9rem;
    }

    .toast-close {
      background: none;
      border: none;
      color: inherit;
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 4px;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      .error-banner {
        padding: 0.75rem;
      }
      
      .error-content {
        flex-direction: column;
        gap: 0.75rem;
      }
      
      .error-actions {
        flex-direction: row;
      }
      
      .error-toast {
        left: 10px;
        right: 10px;
        max-width: none;
      }
    }
  `]
})
export class ErrorDisplayComponent implements OnInit, OnDestroy {
  @Input() showGlobalErrors: boolean = false;
  @Input() inlineError?: AppError;
  @Input() dismissible: boolean = true;
  @Input() showDetails: boolean = false;
  @Input() showToast: boolean = false;
  @Input() toastError?: AppError;
  @Input() autoHideToast: boolean = true;
  @Input() toastDuration: number = 5000;

  @Output() onRetry = new EventEmitter<AppError>();
  @Output() onDismiss = new EventEmitter<void>();

  globalErrors: AppError[] = [];
  expandedErrors: { [key: string]: boolean } = {};
  private subscription: Subscription = new Subscription();
  private toastTimeout?: number;

  constructor(private errorHandlerService: ErrorHandlerService) {}

  ngOnInit() {
    if (this.showGlobalErrors) {
      this.subscription.add(
        this.errorHandlerService.errorState$.subscribe(state => {
          this.globalErrors = state.errors.filter(error => error.userFriendly);
        })
      );
    }

    if (this.showToast && this.toastError && this.autoHideToast) {
      this.scheduleToastHide();
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
  }

  getErrorClass(error: AppError): string {
    return error.type;
  }

  getErrorIcon(error: AppError): string {
    const icons = {
      network: 'fas fa-wifi',
      validation: 'fas fa-exclamation-triangle',
      authentication: 'fas fa-lock',
      authorization: 'fas fa-ban',
      server: 'fas fa-server',
      client: 'fas fa-bug',
      unknown: 'fas fa-question-circle'
    };
    return icons[error.type] || icons.unknown;
  }

  getErrorTitle(error: AppError): string {
    const titles = {
      network: 'Connection Error',
      validation: 'Validation Error',
      authentication: 'Authentication Required',
      authorization: 'Access Denied',
      server: 'Server Error',
      client: 'Application Error',
      unknown: 'Unexpected Error'
    };
    return titles[error.type] || titles.unknown;
  }

  toggleDetails(errorId: string): void {
    this.expandedErrors[errorId] = !this.expandedErrors[errorId];
  }

  dismissError(errorId: string): void {
    this.errorHandlerService.clearError(errorId);
  }

  hideToast(): void {
    this.showToast = false;
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
  }

  private scheduleToastHide(): void {
    this.toastTimeout = window.setTimeout(() => {
      this.hideToast();
    }, this.toastDuration);
  }
}
