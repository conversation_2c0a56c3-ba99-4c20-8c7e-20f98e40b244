.dashboard {
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;
  
  h1 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-weight: 500;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 1rem;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: white;
    }
    
    &.users {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.products {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.orders {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.revenue {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }
  
  .stat-details {
    flex: 1;
    
    h3 {
      margin: 0 0 0.25rem 0;
      font-size: 1.75rem;
      font-weight: 600;
      color: #333;
    }
    
    p {
      margin: 0 0 0.5rem 0;
      color: #666;
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .stat-change {
      font-size: 0.75rem;
      font-weight: 500;
      
      &.positive {
        color: #4caf50;
      }
      
      &.negative {
        color: #f44336;
      }
      
      &.neutral {
        color: #666;
      }
    }
  }
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.analytics-card {
  height: fit-content;
  
  mat-card-header {
    padding-bottom: 1rem;
  }
  
  mat-card-title {
    font-size: 1.125rem;
    font-weight: 500;
  }
  
  mat-card-subtitle {
    font-size: 0.875rem;
    color: #666;
  }
}

.recent-orders {
  .order-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .order-info {
      flex: 1;
      
      .order-number {
        font-weight: 500;
        color: #1976d2;
        margin-bottom: 0.25rem;
      }
      
      .customer-name {
        font-size: 0.875rem;
        color: #666;
      }
    }
    
    .order-amount {
      font-weight: 600;
      color: #2e7d32;
      margin-right: 1rem;
    }
    
    .order-status {
      font-size: 0.75rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

.top-products {
  .product-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .product-rank {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #e3f2fd;
      color: #1976d2;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.875rem;
      margin-right: 1rem;
    }
    
    .product-info {
      flex: 1;
      
      .product-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
      }
      
      .product-sales {
        font-size: 0.875rem;
        color: #666;
      }
    }
    
    .product-revenue {
      font-weight: 600;
      color: #2e7d32;
    }
  }
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
  
  button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    height: auto;
    
    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

.system-status {
  .status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    
    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      
      &.online {
        background: #4caf50;
      }
      
      &.warning {
        background: #ff9800;
      }
      
      &.error {
        background: #f44336;
      }
    }
    
    span {
      font-size: 0.875rem;
      color: #666;
    }
  }
}

.no-orders,
.no-products {
  text-align: center;
  padding: 2rem;
  color: #999;
  
  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
  }
  
  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0.5rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}
