import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

import { Product, ProductsResponse, ProductFilters } from '../models/product.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private readonly API_URL = 'http://localhost:5000/api';

  constructor(private http: HttpClient) {}

  getProducts(filters: ProductFilters = {}): Observable<ProductsResponse> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = (filters as any)[key];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ProductsResponse>(`${this.API_URL}/products`, { params });
  }

  getProduct(id: string): Observable<{ product: Product }> {
    return this.http.get<{ product: Product }>(`${this.API_URL}/products/${id}`);
  }

  createProduct(productData: any): Observable<{ message: string; product: Product }> {
    return this.http.post<{ message: string; product: Product }>(`${this.API_URL}/products`, productData);
  }

  updateProduct(id: string, productData: any): Observable<{ message: string; product: Product }> {
    return this.http.put<{ message: string; product: Product }>(`${this.API_URL}/products/${id}`, productData);
  }

  deleteProduct(id: string): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.API_URL}/products/${id}`);
  }

  addReview(productId: string, reviewData: any): Observable<{ message: string }> {
    return this.http.post<{ message: string }>(`${this.API_URL}/products/${productId}/review`, reviewData);
  }

  getFeaturedProducts(): Observable<{ products: Product[] }> {
    return this.http.get<{ products: Product[] }>(`${this.API_URL}/products/featured`);
  }

  getTrendingProducts(): Observable<{ success: boolean; data: Product[] }> {
    return this.http.get<{ success: boolean; data: Product[] }>(`${this.API_URL}/products/trending`);
  }

  getVendorProducts(vendorId: string, filters: ProductFilters = {}): Observable<ProductsResponse> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = (filters as any)[key];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ProductsResponse>(`${this.API_URL}/products/vendor/${vendorId}`, { params });
  }

  searchProducts(query: string, filters: ProductFilters = {}): Observable<ProductsResponse> {
    const searchFilters = { ...filters, search: query };
    return this.getProducts(searchFilters);
  }

  getCategories(): Observable<{ success: boolean; data: any[] }> {
    return this.http.get<{ success: boolean; data: any[] }>(`${this.API_URL}/categories`);
  }

  getBrands(): Observable<{ brands: string[] }> {
    return this.http.get<{ brands: string[] }>(`${this.API_URL}/products/brands`);
  }

  // Featured Brands
  getFeaturedBrands(): Observable<{ success: boolean; data: any[] }> {
    return this.http.get<{ success: boolean; data: any[] }>(`${this.API_URL}/brands/featured`).pipe(
      catchError(error => {
        console.error('Error fetching featured brands:', error);
        return of({ success: false, data: [], error: error.message });
      })
    );
  }

  // Trending Products
  getTrendingProducts(limit: number = 10): Observable<{ success: boolean; data: Product[] }> {
    return this.http.get<{ success: boolean; data: Product[] }>(`${this.API_URL}/products/trending?limit=${limit}`).pipe(
      catchError(error => {
        console.error('Error fetching trending products:', error);
        return of({ success: false, data: [], error: error.message });
      })
    );
  }

  // New Arrivals
  getNewArrivals(limit: number = 10): Observable<{ success: boolean; data: Product[] }> {
    return this.http.get<{ success: boolean; data: Product[] }>(`${this.API_URL}/products/new-arrivals?limit=${limit}`).pipe(
      catchError(error => {
        console.error('Error fetching new arrivals:', error);
        return of({ success: false, data: [], error: error.message });
      })
    );
  }

  // Get products by brand
  getProductsByBrand(brandId: string, page: number = 1, limit: number = 20): Observable<ProductsResponse> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<ProductsResponse>(`${this.API_URL}/products/brand/${brandId}`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching products by brand:', error);
        return of({
          success: false,
          data: [],
          pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
          error: error.message
        });
      })
    );
  }

  // Get product recommendations
  getRecommendations(productId: string, limit: number = 5): Observable<{ success: boolean; data: Product[] }> {
    return this.http.get<{ success: boolean; data: Product[] }>(`${this.API_URL}/products/${productId}/recommendations?limit=${limit}`).pipe(
      catchError(error => {
        console.error('Error fetching recommendations:', error);
        return of({ success: false, data: [], error: error.message });
      })
    );
  }

  // Get product reviews
  getProductReviews(productId: string, page: number = 1, limit: number = 10): Observable<any> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get(`${this.API_URL}/products/${productId}/reviews`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching product reviews:', error);
        return of({
          success: false,
          data: [],
          pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
          error: error.message
        });
      })
    );
  }

  // Add product review
  addProductReview(productId: string, review: any): Observable<any> {
    return this.http.post(`${this.API_URL}/products/${productId}/reviews`, review).pipe(
      catchError(error => {
        console.error('Error adding product review:', error);
        return throwError(() => error);
      })
    );
  }

  // Product interactions
  toggleProductLike(productId: string): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/products/${productId}/like`, {});
  }

  shareProduct(productId: string): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/products/${productId}/share`, {});
  }

  // Category products
  getCategoryProducts(categorySlug: string, filters: ProductFilters = {}): Observable<ProductsResponse> {
    let params = new HttpParams();

    Object.keys(filters).forEach(key => {
      const value = (filters as any)[key];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ProductsResponse>(`${this.API_URL}/products/category/${categorySlug}`, { params });
  }
}
