# OnlyWomans Backend Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/onlywomans
DB_NAME=onlywomans

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_for_onlywomans_2024
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_key
JWT_REFRESH_EXPIRE=30d

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=OnlyWomans <<EMAIL>>

# Cloudinary Configuration (for image uploads)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Payment Gateway Configuration
# Razorpay (for Indian market)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Stripe (for international)
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# SMS Configuration (for OTP)
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=ONLYWMN

# Social Media Integration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Frontend URLs
WEB_APP_URL=http://localhost:4200
MOBILE_APP_URL=http://localhost:8100
ADMIN_PANEL_URL=http://localhost:4300

# Analytics
GOOGLE_ANALYTICS_ID=your_google_analytics_id
FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# Push Notifications
FCM_SERVER_KEY=your_fcm_server_key
VAPID_PUBLIC_KEY=your_vapid_public_key
VAPID_PRIVATE_KEY=your_vapid_private_key

# Third-party APIs
BEAUTY_API_KEY=your_beauty_api_key
FASHION_TRENDS_API_KEY=your_fashion_trends_api_key

# Logging
LOG_LEVEL=info
LOG_FILE=logs/onlywomans.log
