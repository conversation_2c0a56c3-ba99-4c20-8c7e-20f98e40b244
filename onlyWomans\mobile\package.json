{"name": "onlywomans-mobile", "version": "1.0.0", "description": "OnlyWomans - Women's Fashion E-commerce Mobile App", "author": "OnlyWomans Team", "homepage": "https://onlywomans.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "ionic:build": "ng build", "ionic:serve": "ng serve", "android": "ionic capacitor run android", "ios": "ionic capacitor run ios", "build:android": "ionic capacitor build android", "build:ios": "ionic capacitor build ios", "sync": "ionic capacitor sync", "copy": "ionic capacitor copy"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/service-worker": "^17.0.0", "@capacitor/android": "^5.5.0", "@capacitor/app": "^5.0.6", "@capacitor/core": "^5.5.0", "@capacitor/haptics": "^5.0.6", "@capacitor/ios": "^5.5.0", "@capacitor/keyboard": "^5.0.6", "@capacitor/status-bar": "^5.0.6", "@capacitor/camera": "^5.0.7", "@capacitor/geolocation": "^5.0.6", "@capacitor/push-notifications": "^5.1.0", "@capacitor/share": "^5.0.6", "@capacitor/splash-screen": "^5.0.6", "@ionic/angular": "^7.5.0", "@ionic/storage-angular": "^4.0.0", "ionicons": "^7.2.1", "rxjs": "~7.8.0", "swiper": "^11.0.0", "tslib": "^2.3.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular-eslint/builder": "^17.0.0", "@angular-eslint/eslint-plugin": "^17.0.0", "@angular-eslint/eslint-plugin-template": "^17.0.0", "@angular-eslint/schematics": "^17.0.0", "@angular-eslint/template-parser": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@capacitor/cli": "^5.5.0", "@ionic/angular-toolkit": "^9.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsdoc": "^46.8.0", "eslint-plugin-prefer-arrow": "^1.2.2", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-headless": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0"}, "keywords": ["women", "fashion", "ecommerce", "ionic", "mobile", "shopping", "beauty", "accessories"]}