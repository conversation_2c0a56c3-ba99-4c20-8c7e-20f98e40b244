const express = require('express');
const WomenProduct = require('../models/WomenProduct');
const { auth, optionalAuth } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// @route   GET /api/products
// @desc    Get all women's products with filtering
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build filter object
    let filter = { isActive: true, isDeleted: false };

    // Category filtering
    if (req.query.category) {
      filter.category = req.query.category;
    }

    if (req.query.subCategory) {
      filter.subCategory = req.query.subCategory;
    }

    // Price filtering
    if (req.query.minPrice || req.query.maxPrice) {
      filter.price = {};
      if (req.query.minPrice) filter.price.$gte = parseFloat(req.query.minPrice);
      if (req.query.maxPrice) filter.price.$lte = parseFloat(req.query.maxPrice);
    }

    // Size filtering
    if (req.query.sizes) {
      const sizes = req.query.sizes.split(',');
      filter['sizes.size'] = { $in: sizes };
    }

    // Color filtering
    if (req.query.colors) {
      const colors = req.query.colors.split(',');
      filter['colors.name'] = { $in: colors };
    }

    // Brand filtering
    if (req.query.brands) {
      const brands = req.query.brands.split(',');
      filter.brand = { $in: brands };
    }

    // Occasion filtering
    if (req.query.occasions) {
      const occasions = req.query.occasions.split(',');
      filter.occasions = { $in: occasions };
    }

    // Material filtering
    if (req.query.material) {
      filter['material.fabric'] = { $regex: req.query.material, $options: 'i' };
    }

    // Fit filtering
    if (req.query.fit) {
      filter['fit.type'] = req.query.fit;
    }

    // Search functionality
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    // Special filters
    if (req.query.trending === 'true') filter.trending = true;
    if (req.query.featured === 'true') filter.featured = true;
    if (req.query.newArrival === 'true') filter.newArrival = true;
    if (req.query.bestSeller === 'true') filter.bestSeller = true;

    // Rating filter
    if (req.query.minRating) {
      filter['rating.average'] = { $gte: parseFloat(req.query.minRating) };
    }

    // Build sort object
    let sort = {};
    switch (req.query.sortBy) {
      case 'price_asc':
        sort.price = 1;
        break;
      case 'price_desc':
        sort.price = -1;
        break;
      case 'rating':
        sort['rating.average'] = -1;
        break;
      case 'popularity':
        sort['analytics.views'] = -1;
        break;
      case 'newest':
        sort.createdAt = -1;
        break;
      case 'name':
        sort.name = 1;
        break;
      default:
        sort.featured = -1;
        sort.trending = -1;
        sort.createdAt = -1;
    }

    // Execute query
    const products = await WomenProduct.find(filter)
      .populate('vendor', 'firstName lastName profile.avatar')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await WomenProduct.countDocuments(filter);

    // Add user-specific data if authenticated
    if (req.user) {
      // Add wishlist status, cart status, etc.
      // This would require additional queries to user's wishlist/cart
    }

    res.json({
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      },
      filters: {
        categories: await WomenProduct.distinct('category', { isActive: true }),
        subCategories: await WomenProduct.distinct('subCategory', { isActive: true }),
        brands: await WomenProduct.distinct('brand', { isActive: true }),
        priceRange: await WomenProduct.aggregate([
          { $match: { isActive: true } },
          { $group: { _id: null, min: { $min: '$price' }, max: { $max: '$price' } } }
        ])
      }
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: error.message
    });
  }
});

// @route   GET /api/products/trending
// @desc    Get trending women's products
// @access  Public
router.get('/trending', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;
    const category = req.query.category;

    let filter = { 
      isActive: true, 
      isDeleted: false,
      $or: [
        { trending: true },
        { 'analytics.views': { $gte: 100 } },
        { 'analytics.cartAdds': { $gte: 10 } }
      ]
    };

    if (category) {
      filter.category = category;
    }

    const products = await WomenProduct.find(filter)
      .populate('vendor', 'firstName lastName profile.avatar')
      .sort({ 
        trending: -1, 
        'analytics.views': -1, 
        'analytics.cartAdds': -1,
        'rating.average': -1 
      })
      .limit(limit)
      .lean();

    res.json({
      success: true,
      data: products
    });

  } catch (error) {
    console.error('Get trending products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trending products',
      error: error.message
    });
  }
});

// @route   GET /api/products/new-arrivals
// @desc    Get new arrival women's products
// @access  Public
router.get('/new-arrivals', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;
    const category = req.query.category;
    const days = parseInt(req.query.days) || 30; // Products added in last 30 days

    let filter = { 
      isActive: true, 
      isDeleted: false,
      createdAt: { $gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000) }
    };

    if (category) {
      filter.category = category;
    }

    const products = await WomenProduct.find(filter)
      .populate('vendor', 'firstName lastName profile.avatar')
      .sort({ createdAt: -1, newArrival: -1 })
      .limit(limit)
      .lean();

    res.json({
      success: true,
      data: products
    });

  } catch (error) {
    console.error('Get new arrivals error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch new arrivals',
      error: error.message
    });
  }
});

// @route   GET /api/products/featured
// @desc    Get featured women's products
// @access  Public
router.get('/featured', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;
    const category = req.query.category;

    let filter = { 
      isActive: true, 
      isDeleted: false,
      featured: true
    };

    if (category) {
      filter.category = category;
    }

    const products = await WomenProduct.find(filter)
      .populate('vendor', 'firstName lastName profile.avatar')
      .sort({ 'rating.average': -1, 'analytics.views': -1 })
      .limit(limit)
      .lean();

    res.json({
      success: true,
      data: products
    });

  } catch (error) {
    console.error('Get featured products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch featured products',
      error: error.message
    });
  }
});

// @route   GET /api/products/categories/:category
// @desc    Get products by category
// @access  Public
router.get('/categories/:category', optionalAuth, async (req, res) => {
  try {
    const category = req.params.category;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    let filter = { 
      category: category,
      isActive: true, 
      isDeleted: false 
    };

    // Additional filters
    if (req.query.subCategory) {
      filter.subCategory = req.query.subCategory;
    }

    if (req.query.minPrice || req.query.maxPrice) {
      filter.price = {};
      if (req.query.minPrice) filter.price.$gte = parseFloat(req.query.minPrice);
      if (req.query.maxPrice) filter.price.$lte = parseFloat(req.query.maxPrice);
    }

    // Sort
    let sort = {};
    switch (req.query.sortBy) {
      case 'price_asc': sort.price = 1; break;
      case 'price_desc': sort.price = -1; break;
      case 'rating': sort['rating.average'] = -1; break;
      case 'popularity': sort['analytics.views'] = -1; break;
      default: sort.featured = -1; sort.createdAt = -1;
    }

    const products = await WomenProduct.find(filter)
      .populate('vendor', 'firstName lastName profile.avatar')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const total = await WomenProduct.countDocuments(filter);

    // Get category-specific filters
    const subCategories = await WomenProduct.distinct('subCategory', { category, isActive: true });
    const brands = await WomenProduct.distinct('brand', { category, isActive: true });
    const occasions = await WomenProduct.distinct('occasions', { category, isActive: true }).then(
      results => results.flat()
    );

    res.json({
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      categoryInfo: {
        category,
        subCategories,
        brands,
        occasions: [...new Set(occasions)]
      }
    });

  } catch (error) {
    console.error('Get category products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category products',
      error: error.message
    });
  }
});

module.exports = router;
