.admin-header {
  background: white;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 1rem;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.menu-toggle {
  margin-right: 1rem;

  &.mobile-only {
    display: none;
  }
}

.page-title {
  flex: 1;

  h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
    color: #333;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-container {
  .search-field {
    width: 300px;

    ::ng-deep {
      .mat-form-field-wrapper {
        padding-bottom: 0;
      }

      .mat-form-field-infix {
        padding: 0.5rem 0;
      }

      .mat-form-field-outline {
        top: 0;
      }

      .mat-form-field-outline-start,
      .mat-form-field-outline-end {
        border-radius: 20px;
      }

      .mat-form-field-outline-gap {
        border-radius: 0;
      }
    }
  }
}

.notification-button {
  position: relative;

  ::ng-deep {
    .mat-badge-content {
      font-size: 0.625rem;
      font-weight: 600;
    }
  }
}

.notification-menu {
  width: 350px;
  max-height: 400px;

  .notification-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 500;
    }
  }

  .notification-list {
    max-height: 250px;
    overflow-y: auto;

    .notification-item {
      width: 100%;
      padding: 1rem;
      text-align: left;
      border: none;
      background: none;
      cursor: pointer;
      transition: background 0.2s ease;

      &:hover {
        background: #f5f5f5;
      }

      &.unread {
        background: #e3f2fd;

        &:hover {
          background: #bbdefb;
        }
      }

      .notification-content {
        .notification-title {
          font-weight: 500;
          margin-bottom: 0.25rem;
          color: #333;
        }

        .notification-message {
          font-size: 0.875rem;
          color: #666;
          margin-bottom: 0.25rem;
          line-height: 1.3;
        }

        .notification-time {
          font-size: 0.75rem;
          color: #999;
        }
      }
    }
  }

  .view-all-button {
    width: 100%;
    justify-content: center;
    font-weight: 500;
  }
}

.user-profile-button {
  mat-icon {
    font-size: 1.75rem;
    width: 1.75rem;
    height: 1.75rem;
  }
}

.user-menu-header {
  padding: 1rem;

  .user-info {
    .user-name {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .user-email {
      font-size: 0.875rem;
      color: #666;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .admin-header {
    padding: 0 0.5rem;
  }

  .menu-toggle.mobile-only {
    display: block;
  }

  .page-title h1 {
    font-size: 1.25rem;
  }

  .search-container {
    display: none;
  }

  .header-actions {
    gap: 0.25rem;
  }

  .notification-menu {
    width: 300px;
  }
}

@media (max-width: 480px) {
  .page-title {
    display: none;
  }

  .notification-menu {
    width: 280px;
  }
}

// Custom scrollbar for notification list
.notification-list {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
