// Instagram-style Mobile Posts
.posts-feed {
  background: var(--ion-color-light);
}

.post-item {
  background: #fff;
  margin-bottom: 8px;
  border-radius: 0;
  overflow: hidden;
}

// Post Header
.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    
    .user-avatar {
      width: 32px;
      height: 32px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .user-details {
      display: flex;
      flex-direction: column;
      
      .username {
        font-weight: 600;
        font-size: 14px;
        color: var(--ion-color-dark);
      }
      
      .timestamp {
        font-size: 12px;
        color: var(--ion-color-medium);
      }
    }
  }
}

// Post Media
.post-media {
  position: relative;
  width: 100%;
  background: #000;
  
  .media-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1; // Square aspect ratio like Instagram
    overflow: hidden;
    
    .post-image,
    .post-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
    
    .post-video {
      background: #000;
    }
  }
}

// Product Tags (Instagram-style) - Only show when tapped or hovered
.product-tags {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;

  &.show-tags {
    opacity: 1;
  }

  .product-tag {
    position: absolute;
    pointer-events: all;
    cursor: pointer;
    transform: translate(-50%, -50%);

    .product-dot {
      position: relative;
      width: 24px;
      height: 24px;
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid #000;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 12px rgba(0,0,0,0.3);
      backdrop-filter: blur(10px);

      &::before {
        content: '';
        width: 8px;
        height: 8px;
        background: #000;
        border-radius: 50%;
      }

      .product-pulse {
        position: absolute;
        width: 140%;
        height: 140%;
        border: 2px solid rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: productPulse 2s infinite;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    // Product info tooltip
    .product-info {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      white-space: nowrap;
      margin-bottom: 8px;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s ease;

      &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.8);
      }
    }

    &:hover .product-info {
      opacity: 1;
    }
  }
}

// Shopping indicator (Instagram-style)
.shopping-indicator {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6px 8px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.3s ease;

  ion-icon {
    font-size: 14px;
  }

  &::after {
    content: 'Tap to view products';
    font-size: 10px;
    margin-left: 4px;
  }
}

@keyframes productPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

// Post Actions
.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px 0;
  
  .action-buttons {
    display: flex;
    gap: 4px;
    
    ion-button {
      --padding-start: 8px;
      --padding-end: 8px;
      height: 40px;
      width: 40px;
      
      ion-icon {
        font-size: 24px;
      }
    }
  }
}

// Post Stats
.post-stats {
  padding: 0 16px 12px;
  
  .likes-count {
    margin-bottom: 4px;
    
    strong {
      font-size: 14px;
      color: var(--ion-color-dark);
    }
  }
  
  .post-caption {
    margin-bottom: 4px;
    line-height: 1.4;
    
    .username {
      font-weight: 600;
      color: var(--ion-color-dark);
      margin-right: 4px;
    }
    
    .caption-text {
      color: var(--ion-color-dark);
      font-size: 14px;
    }
  }
  
  .comments-preview {
    .view-comments {
      color: var(--ion-color-medium);
      font-size: 14px;
      cursor: pointer;
    }
  }
}

// Product Showcase
.product-showcase {
  border-top: 1px solid var(--ion-color-light-shade);
  padding: 12px 16px;
  background: #fafafa;
  
  .showcase-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    span {
      font-weight: 600;
      font-size: 14px;
      color: var(--ion-color-dark);
    }
    
    ion-icon {
      color: var(--ion-color-primary);
    }
  }
  
  .products-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .product-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px;
      background: #fff;
      border-radius: 8px;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:active {
        transform: scale(0.98);
      }
      
      .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 6px;
      }
      
      .product-info {
        flex: 1;
        
        .product-name {
          display: block;
          font-weight: 600;
          font-size: 14px;
          color: var(--ion-color-dark);
          margin-bottom: 2px;
        }
        
        .product-brand {
          display: block;
          font-size: 12px;
          color: var(--ion-color-medium);
          margin-bottom: 4px;
        }
        
        .product-price {
          .current-price {
            font-weight: 600;
            color: var(--ion-color-primary);
            margin-right: 6px;
          }
          
          .original-price {
            font-size: 12px;
            text-decoration: line-through;
            color: var(--ion-color-medium);
          }
        }
      }
      
      .product-actions {
        display: flex;
        gap: 4px;
        
        ion-button {
          --padding-start: 8px;
          --padding-end: 8px;
          height: 32px;
          width: 32px;
        }
      }
    }
  }
}

// Comments Section
.comments-section {
  border-top: 1px solid var(--ion-color-light-shade);
  background: #fff;
  
  .comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--ion-color-light-shade);
    
    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .add-comment {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-bottom: 1px solid var(--ion-color-light-shade);
    
    ion-input {
      flex: 1;
    }
  }
  
  .comments-list {
    max-height: 300px;
    overflow-y: auto;
    
    .comment-item {
      display: flex;
      gap: 12px;
      padding: 12px 16px;
      
      .comment-avatar {
        width: 32px;
        height: 32px;
        flex-shrink: 0;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .comment-content {
        flex: 1;
        
        .comment-username {
          font-weight: 600;
          font-size: 14px;
          color: var(--ion-color-dark);
          margin-right: 6px;
        }
        
        .comment-text {
          font-size: 14px;
          color: var(--ion-color-dark);
        }
        
        .comment-meta {
          display: flex;
          gap: 16px;
          margin-top: 4px;
          
          span {
            font-size: 12px;
            color: var(--ion-color-medium);
            cursor: pointer;
          }
        }
      }
    }
  }
}

// Loading & Empty States
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 32px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

.empty-posts {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 32px;
  
  .empty-content {
    text-align: center;
    
    ion-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      color: var(--ion-color-dark);
    }
    
    p {
      margin: 0;
      color: var(--ion-color-medium);
    }
  }
}

// Responsive Design
@media (max-width: 576px) {
  .post-header {
    padding: 8px 12px;
  }
  
  .post-actions {
    padding: 6px 12px 0;
  }
  
  .post-stats {
    padding: 0 12px 8px;
  }
  
  .product-showcase {
    padding: 8px 12px;
  }
  
  .comments-section {
    .comments-header,
    .add-comment {
      padding: 8px 12px;
    }
    
    .comment-item {
      padding: 8px 12px;
    }
  }
}
