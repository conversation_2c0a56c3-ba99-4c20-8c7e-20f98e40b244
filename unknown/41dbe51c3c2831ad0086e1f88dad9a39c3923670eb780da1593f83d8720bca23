.shop-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Search Section
.search-section {
  margin-bottom: 40px;
  
  .search-bar {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    overflow: hidden;
    
    .search-input {
      flex: 1;
      padding: 12px 20px;
      border: none;
      outline: none;
      font-size: 16px;
    }
    
    .search-btn {
      padding: 12px 20px;
      background: #007bff;
      color: white;
      border: none;
      cursor: pointer;
      
      &:hover {
        background: #0056b3;
      }
    }
  }
}

// Section Titles
.section-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 30px;
  text-align: center;
  color: #333;
}

// Category Section
.category-section {
  margin-bottom: 60px;
  
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    
    .category-card {
      background: white;
      border-radius: 15px;
      padding: 30px 20px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      }
      
      .category-icon {
        font-size: 48px;
        margin-bottom: 15px;
      }
      
      .category-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }
  }
}

// Featured Brands Section
.featured-brands-section {
  margin-bottom: 60px;
  
  .brands-slider {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding: 20px 0;
    
    .brand-card {
      min-width: 200px;
      background: white;
      border-radius: 15px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      position: relative;
      
      .brand-logo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        margin-bottom: 15px;
      }
      
      .brand-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
      
      .popular-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #ff6b6b;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
}

// Products Grid
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
  
  .product-image-container {
    position: relative;
    
    .product-image {
      width: 100%;
      height: 250px;
      object-fit: cover;
    }
    
    .discount-badge, .new-badge {
      position: absolute;
      top: 10px;
      left: 10px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      color: white;
    }
    
    .discount-badge {
      background: #ff6b6b;
    }
    
    .new-badge {
      background: #4ecdc4;
    }
    
    .product-actions {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: none;
        background: rgba(255,255,255,0.9);
        color: #666;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: all 0.3s ease;
        
        &:hover {
          background: white;
          color: #333;
        }
        
        &.liked {
          color: #ff6b6b;
        }
        
        span {
          font-size: 10px;
          margin-left: 2px;
        }
      }
    }
  }
  
  .product-info {
    padding: 20px;
    
    .product-name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
      line-height: 1.3;
    }
    
    .product-brand {
      color: #666;
      font-size: 14px;
      margin: 0 0 12px 0;
    }
    
    .product-pricing {
      margin-bottom: 15px;
      
      .current-price {
        font-size: 20px;
        font-weight: 600;
        color: #333;
      }
      
      .original-price {
        font-size: 16px;
        color: #999;
        text-decoration: line-through;
        margin-left: 8px;
      }
    }
    
    .product-buttons {
      display: flex;
      gap: 10px;
      
      button {
        flex: 1;
        padding: 10px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        
        i {
          margin-right: 5px;
        }
      }
      
      .btn-wishlist {
        background: #f8f9fa;
        color: #666;
        
        &:hover {
          background: #e9ecef;
          color: #333;
        }
      }
      
      .btn-cart {
        background: #007bff;
        color: white;
        
        &:hover {
          background: #0056b3;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .shop-container {
    padding: 15px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .brands-slider {
    .brand-card {
      min-width: 150px;
    }
  }
}
