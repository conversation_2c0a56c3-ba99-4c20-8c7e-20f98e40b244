<div class="login-container">
  <div class="login-card">
    <!-- Header -->
    <div class="login-header">
      <div class="logo">
        <mat-icon class="logo-icon">shopping_bag</mat-icon>
        <h1>DFashion Admin</h1>
      </div>
      <p class="subtitle">Sign in to your admin account</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <!-- Email Field -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Email Address</mat-label>
        <input matInput 
               type="email" 
               formControlName="email"
               placeholder="Enter your email"
               autocomplete="email">
        <mat-icon matSuffix>email</mat-icon>
        <mat-error *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
          {{ getErrorMessage('email') }}
        </mat-error>
      </mat-form-field>

      <!-- Password Field -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Password</mat-label>
        <input matInput 
               [type]="hidePassword ? 'password' : 'text'"
               formControlName="password"
               placeholder="Enter your password"
               autocomplete="current-password">
        <button mat-icon-button 
                matSuffix 
                type="button"
                (click)="hidePassword = !hidePassword"
                [attr.aria-label]="'Hide password'"
                [attr.aria-pressed]="hidePassword">
          <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
          {{ getErrorMessage('password') }}
        </mat-error>
      </mat-form-field>

      <!-- Login Button -->
      <button mat-raised-button 
              color="primary" 
              type="submit"
              class="login-button full-width"
              [disabled]="isLoading">
        <mat-spinner *ngIf="isLoading" diameter="20" class="login-spinner"></mat-spinner>
        <span *ngIf="!isLoading">Sign In</span>
        <span *ngIf="isLoading">Signing In...</span>
      </button>
    </form>



    <!-- Footer -->
    <div class="login-footer">
      <p>&copy; 2024 DFashion. All rights reserved.</p>
      <div class="footer-links">
        <a href="#" class="footer-link">Privacy Policy</a>
        <span class="separator">•</span>
        <a href="#" class="footer-link">Terms of Service</a>
      </div>
    </div>
  </div>

  <!-- Background -->
  <div class="login-background">
    <div class="bg-pattern"></div>
  </div>
</div>
