// Mobile Layout Container
.mobile-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

// Mobile Header
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  
  // Safe area support for iOS
  padding-top: env(safe-area-inset-top);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  height: 56px;
}

.header-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background: #f0f0f0;
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  i {
    font-size: 18px;
    color: #333;
  }
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  text-decoration: none;
  
  .logo-image {
    width: 32px;
    height: 32px;
    border-radius: 6px;
  }
  
  .logo-text {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ff6b6b;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
}

// Mobile Search
.mobile-search {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  
  &.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
}

.search-container {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
}

.mobile-search-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  
  &:focus {
    border-color: #667eea;
  }
}

.search-submit-btn,
.search-close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #667eea;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    transform: scale(0.95);
  }
}

.search-close-btn {
  background: #f0f0f0;
  color: #666;
}

// Mobile Side Menu
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  visibility: hidden;
  
  &.active {
    visibility: visible;
    
    .menu-overlay {
      opacity: 1;
    }
    
    .menu-content {
      transform: translateX(0);
    }
  }
}

.menu-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-content {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  background: white;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  
  // Safe area support
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

// Menu Profile Section
.menu-profile {
  padding: 24px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.profile-info {
  flex: 1;
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 4px 0;
  }
  
  p {
    font-size: 14px;
    opacity: 0.8;
    margin: 0;
  }
}

// Menu Guest Section
.menu-guest {
  padding: 24px 20px;
  background: #f8f9fa;
  text-align: center;
}

.guest-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  
  i {
    font-size: 24px;
    color: #666;
  }
}

.guest-info {
  margin-bottom: 20px;
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.guest-actions {
  display: flex;
  gap: 8px;
  
  .btn-primary,
  .btn-secondary {
    flex: 1;
    padding: 10px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
  }
  
  .btn-primary {
    background: #667eea;
    color: white;
    border: none;
    
    &:hover {
      background: #5a6fd8;
    }
  }
  
  .btn-secondary {
    background: white;
    color: #667eea;
    border: 1px solid #667eea;
    
    &:hover {
      background: #f0f2ff;
    }
  }
}

// Menu Navigation
.menu-nav {
  flex: 1;
  padding: 8px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  border: none;
  background: none;
  width: 100%;
  cursor: pointer;
  
  &:hover {
    background: #f8f9fa;
  }
  
  &.active {
    background: #e7f3ff;
    color: #667eea;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: #667eea;
    }
  }
  
  i {
    font-size: 18px;
    width: 20px;
    text-align: center;
  }
  
  span {
    font-size: 16px;
    font-weight: 500;
  }
  
  &.logout-btn {
    color: #dc3545;
    
    &:hover {
      background: #fff5f5;
    }
  }
}

.menu-badge {
  margin-left: auto;
  background: #ff6b6b;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.menu-section {
  .menu-divider {
    height: 1px;
    background: #e0e0e0;
    margin: 8px 20px;
  }
}

// Menu Footer
.menu-footer {
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.app-info {
  text-align: center;
  margin-bottom: 16px;
  
  p {
    font-size: 12px;
    color: #666;
    margin: 4px 0;
  }
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.social-link {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  
  &:hover {
    background: #667eea;
    color: white;
  }
  
  i {
    font-size: 16px;
  }
}

// Main Content
.mobile-main {
  flex: 1;
  padding-top: calc(56px + env(safe-area-inset-top));
  padding-bottom: 80px; // Space for bottom nav
  
  .keyboard-open & {
    padding-bottom: 0;
  }
}

// Mobile Bottom Navigation
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  z-index: 1000;
  
  // Safe area support
  padding-bottom: env(safe-area-inset-bottom);
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  min-height: 60px;
  
  &:hover {
    background: #f8f9fa;
  }
  
  &.active {
    color: #667eea;
    
    i {
      transform: scale(1.1);
    }
  }
  
  i {
    font-size: 20px;
    margin-bottom: 4px;
    transition: transform 0.3s ease;
  }
  
  span {
    font-size: 10px;
    font-weight: 600;
    text-align: center;
  }
}

.nav-badge {
  position: absolute;
  top: 4px;
  right: 50%;
  transform: translateX(50%);
  background: #ff6b6b;
  color: white;
  font-size: 8px;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 8px;
  min-width: 14px;
  text-align: center;
  line-height: 1;
}

// Mobile Footer
.mobile-footer {
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  padding: 20px 16px;
  padding-bottom: calc(20px + env(safe-area-inset-bottom));
}

.footer-content {
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 16px;
  
  a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    
    &:hover {
      color: #667eea;
    }
  }
}

.footer-copyright {
  p {
    font-size: 12px;
    color: #999;
    margin: 0;
  }
}

// Responsive Adjustments
@media (max-width: 480px) {
  .header-content {
    padding: 8px 12px;
  }
  
  .menu-content {
    width: 100%;
    max-width: 320px;
  }
  
  .nav-item {
    padding: 6px 2px;
    min-height: 56px;
    
    i {
      font-size: 18px;
    }
    
    span {
      font-size: 9px;
    }
  }
}

// Landscape Mode Adjustments
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-main {
    padding-top: 48px;
    padding-bottom: 0;
  }
  
  .mobile-bottom-nav {
    display: none;
  }
  
  .menu-profile,
  .menu-guest {
    padding: 16px 20px;
  }
  
  .profile-avatar,
  .guest-avatar {
    width: 40px;
    height: 40px;
  }
}

// Dark Mode Support (if implemented)
@media (prefers-color-scheme: dark) {
  .mobile-header,
  .mobile-search,
  .menu-content,
  .mobile-bottom-nav,
  .mobile-footer {
    background: #1a1a1a;
    border-color: #333;
  }
  
  .header-btn,
  .menu-item {
    color: #fff;
    
    &:hover {
      background: #333;
    }
  }
  
  .mobile-search-input {
    background: #333;
    border-color: #555;
    color: #fff;
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .mobile-header,
  .mobile-bottom-nav {
    border-width: 2px;
  }
  
  .badge,
  .nav-badge,
  .menu-badge {
    border: 1px solid #000;
  }
}
