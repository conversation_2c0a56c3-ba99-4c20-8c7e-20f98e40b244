import { Component, OnInit } from '@angular/core';
import { Platform } from '@ionic/angular';
import { StatusBar, Style } from '@capacitor/status-bar';
import { SplashScreen } from '@capacitor/splash-screen';
import { register } from 'swiper/element/bundle';

register();

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnInit {
  public appPages = [
    { title: 'Home', url: '/home', icon: 'home' },
    { title: 'Shop', url: '/shop', icon: 'storefront' },
    { title: 'Categories', url: '/categories', icon: 'grid' },
    { title: 'Beauty', url: '/beauty', icon: 'color-palette' },
    { title: 'Ethnic Wear', url: '/ethnic', icon: 'flower' },
    { title: 'Western Wear', url: '/western', icon: 'shirt' },
    { title: 'Accessories', url: '/accessories', icon: 'bag' },
    { title: 'My Orders', url: '/orders', icon: 'receipt' },
    { title: 'Wishlist', url: '/wishlist', icon: 'heart' },
    { title: 'Profile', url: '/profile', icon: 'person' }
  ];

  public labels = [
    'New Arrivals',
    'Trending',
    'Sale',
    'Premium Collection'
  ];

  constructor(private platform: Platform) {
    this.initializeApp();
  }

  ngOnInit() {
    // Additional initialization if needed
  }

  initializeApp() {
    this.platform.ready().then(() => {
      this.setupStatusBar();
      this.hideSplashScreen();
    });
  }

  async setupStatusBar() {
    try {
      await StatusBar.setStyle({ style: Style.Light });
      await StatusBar.setBackgroundColor({ color: '#ec4899' }); // Primary pink color
    } catch (error) {
      console.log('StatusBar not available:', error);
    }
  }

  async hideSplashScreen() {
    try {
      await SplashScreen.hide();
    } catch (error) {
      console.log('SplashScreen not available:', error);
    }
  }
}
