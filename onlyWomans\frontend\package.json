{"name": "onlywomans-frontend", "version": "1.0.0", "description": "OnlyWomans - Women's Fashion E-commerce Web App", "scripts": {"ng": "ng", "start": "ng serve --port 4201", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "e2e": "ng e2e", "lint": "ng lint", "serve:prod": "ng serve --configuration production"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/service-worker": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/material": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "swiper": "^11.0.0", "aos": "^2.3.4", "lottie-web": "^5.12.2", "ngx-lottie": "^10.0.0", "ngx-swiper-wrapper": "^10.0.0", "ngx-infinite-scroll": "^17.0.0", "ngx-loading": "^17.0.0", "ngx-toastr": "^18.0.0", "ngx-spinner": "^17.0.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-headless": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0"}, "keywords": ["women", "fashion", "ecommerce", "angular", "shopping", "beauty", "accessories"], "author": "OnlyWomans Team", "license": "MIT"}