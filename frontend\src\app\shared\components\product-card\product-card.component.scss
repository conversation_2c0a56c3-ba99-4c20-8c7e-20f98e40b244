.product-card {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &.featured {
    border: 2px solid var(--ion-color-warning);
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.featured-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: var(--ion-color-warning);
  color: var(--ion-color-warning-contrast);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;

  ion-icon {
    font-size: 0.875rem;
  }
}

.discount-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: var(--ion-color-danger);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.wishlist-button {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  backdrop-filter: blur(4px);

  ion-icon {
    font-size: 1.25rem;
  }
}

.product-info {
  padding: 16px;
  cursor: pointer;

  .brand {
    font-size: 0.75rem;
    color: var(--ion-color-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
  }

  .product-name {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--ion-color-dark);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .description {
    font-size: 0.875rem;
    color: var(--ion-color-medium);
    margin: 0 0 8px 0;
    line-height: 1.4;
  }

  app-rating {
    margin-bottom: 8px;
  }
}

.action-buttons {
  padding: 0 16px 16px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;

  ion-button {
    margin: 0;
    height: 36px;
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .image-container {
    height: 180px;
  }

  .product-info {
    padding: 12px;

    .product-name {
      font-size: 0.875rem;
    }
  }

  .action-buttons {
    padding: 0 12px 12px 12px;
  }
}
