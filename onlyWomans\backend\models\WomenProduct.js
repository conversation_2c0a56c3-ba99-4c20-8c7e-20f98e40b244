const mongoose = require('mongoose');

const womenProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  shortDescription: {
    type: String,
    maxlength: 300
  },
  
  // Women-specific categories
  category: {
    type: String,
    required: true,
    enum: [
      'ethnic-wear',
      'western-wear', 
      'party-wear',
      'casual-wear',
      'work-wear',
      'lingerie',
      'sleepwear',
      'maternity',
      'plus-size',
      'accessories',
      'footwear',
      'bags-purses',
      'jewelry',
      'beauty-makeup',
      'skincare',
      'haircare',
      'fragrances',
      'home-decor',
      'fitness-wear'
    ]
  },
  
  // Sub-categories for better organization
  subCategory: {
    type: String,
    required: true,
    enum: [
      // Ethnic Wear
      'sarees', 'lehengas', 'salwar-suits', 'kurtis', 'palazzo-sets', 'anarkali', 'sharara', 'gharara',
      // Western Wear
      'dresses', 'tops-blouses', 'jeans', 'trousers', 'skirts', 'shorts', 'jumpsuits', 'co-ord-sets',
      // Party Wear
      'cocktail-dresses', 'gowns', 'party-tops', 'sequin-wear', 'designer-wear',
      // Accessories
      'earrings', 'necklaces', 'bracelets', 'rings', 'watches', 'hair-accessories', 'scarves',
      // Footwear
      'heels', 'flats', 'sandals', 'boots', 'sneakers', 'ethnic-footwear',
      // Bags
      'handbags', 'clutches', 'tote-bags', 'sling-bags', 'backpacks', 'wallets',
      // Beauty
      'lipstick', 'foundation', 'eyeshadow', 'mascara', 'nail-polish', 'skincare-sets',
      // Others
      'lingerie-sets', 'bras', 'panties', 'nightwear', 'maternity-wear', 'plus-size-clothing'
    ]
  },
  
  // Pricing
  price: {
    type: Number,
    required: true,
    min: 0
  },
  originalPrice: {
    type: Number,
    min: 0
  },
  discount: {
    percentage: { type: Number, min: 0, max: 100 },
    amount: { type: Number, min: 0 }
  },
  
  // Women-specific attributes
  sizes: [{
    size: {
      type: String,
      enum: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', '28', '30', '32', '34', '36', '38', '40', '42', '44', 'Free Size', 'One Size']
    },
    stock: { type: Number, default: 0 },
    price: { type: Number } // Size-specific pricing
  }],
  
  colors: [{
    name: { type: String, required: true },
    hexCode: { type: String },
    images: [String],
    stock: { type: Number, default: 0 }
  }],
  
  // Material and care
  material: {
    fabric: { type: String }, // Cotton, Silk, Chiffon, Georgette, etc.
    blend: { type: String },
    care: [String], // Dry clean only, Hand wash, Machine wash, etc.
    transparency: { type: String, enum: ['Opaque', 'Semi-transparent', 'Transparent'] },
    stretch: { type: String, enum: ['No stretch', 'Slight stretch', 'Moderate stretch', 'High stretch'] }
  },
  
  // Fit and style
  fit: {
    type: { type: String, enum: ['Regular', 'Slim', 'Loose', 'Oversized', 'Bodycon', 'A-line', 'Straight'] },
    length: { type: String, enum: ['Crop', 'Regular', 'Long', 'Maxi', 'Mini', 'Midi', 'Knee-length'] },
    neckline: { type: String, enum: ['Round', 'V-neck', 'Scoop', 'High neck', 'Off-shoulder', 'Halter', 'Strapless'] },
    sleeves: { type: String, enum: ['Sleeveless', 'Short sleeve', 'Long sleeve', '3/4 sleeve', 'Cap sleeve', 'Bell sleeve'] }
  },
  
  // Occasion tags
  occasions: [{
    type: String,
    enum: [
      'casual', 'formal', 'party', 'wedding', 'festival', 'office', 'date-night', 
      'vacation', 'brunch', 'cocktail', 'ethnic-celebration', 'maternity-friendly',
      'plus-size-friendly', 'work-from-home', 'gym-workout', 'yoga', 'travel'
    ]
  }],
  
  // Images with women-focused metadata
  images: [{
    url: { type: String, required: true },
    alt: { type: String },
    type: { 
      type: String, 
      enum: ['main', 'back', 'side', 'detail', 'model-front', 'model-back', 'styling', 'size-chart'],
      default: 'main'
    },
    modelInfo: {
      height: String, // "5'6"
      size: String,   // "Size M"
      measurements: String // "Bust: 34", Waist: 28", Hips: 36""
    }
  }],
  
  // Brand and vendor
  brand: {
    type: String,
    required: true
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Inventory
  totalStock: {
    type: Number,
    default: 0
  },
  lowStockThreshold: {
    type: Number,
    default: 5
  },
  
  // SEO and marketing
  tags: [String],
  trending: {
    type: Boolean,
    default: false
  },
  featured: {
    type: Boolean,
    default: false
  },
  newArrival: {
    type: Boolean,
    default: false
  },
  bestSeller: {
    type: Boolean,
    default: false
  },
  
  // Reviews and ratings
  rating: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 },
    breakdown: {
      5: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      1: { type: Number, default: 0 }
    }
  },
  
  // Analytics
  analytics: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    cartAdds: { type: Number, default: 0 },
    purchases: { type: Number, default: 0 },
    wishlistAdds: { type: Number, default: 0 }
  },
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
womenProductSchema.index({ category: 1, subCategory: 1 });
womenProductSchema.index({ brand: 1 });
womenProductSchema.index({ price: 1 });
womenProductSchema.index({ 'rating.average': -1 });
womenProductSchema.index({ createdAt: -1 });
womenProductSchema.index({ trending: -1, featured: -1 });
womenProductSchema.index({ 'analytics.views': -1 });
womenProductSchema.index({ name: 'text', description: 'text', tags: 'text' });

// Virtual for discount calculation
womenProductSchema.virtual('discountedPrice').get(function() {
  if (this.originalPrice && this.originalPrice > this.price) {
    return this.price;
  }
  return this.price;
});

// Virtual for discount percentage
womenProductSchema.virtual('discountPercentage').get(function() {
  if (this.originalPrice && this.originalPrice > this.price) {
    return Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100);
  }
  return 0;
});

// Pre-save middleware
womenProductSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Calculate total stock from sizes and colors
  let totalStock = 0;
  this.sizes.forEach(size => totalStock += size.stock || 0);
  this.colors.forEach(color => totalStock += color.stock || 0);
  
  if (totalStock > 0) {
    this.totalStock = Math.max(this.totalStock, totalStock);
  }
  
  next();
});

// Methods
womenProductSchema.methods.updateRating = function(newRating) {
  const ratings = this.rating.breakdown;
  ratings[newRating] += 1;
  this.rating.count += 1;
  
  const total = (ratings[5] * 5) + (ratings[4] * 4) + (ratings[3] * 3) + (ratings[2] * 2) + (ratings[1] * 1);
  this.rating.average = total / this.rating.count;
  
  return this.save();
};

womenProductSchema.methods.incrementView = function() {
  this.analytics.views += 1;
  return this.save();
};

womenProductSchema.methods.isInStock = function(size = null, color = null) {
  if (size && color) {
    const sizeStock = this.sizes.find(s => s.size === size)?.stock || 0;
    const colorStock = this.colors.find(c => c.name === color)?.stock || 0;
    return Math.min(sizeStock, colorStock) > 0;
  }
  return this.totalStock > 0;
};

module.exports = mongoose.model('WomenProduct', womenProductSchema);
