import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../../../core/services/auth.service';
import { RoleAccessService } from '../../../../core/services/role-access.service';
import { RoleAccessDirective, RoleClassDirective, RoleDisableDirective } from '../../../../shared/directives/role-access.directive';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, FormsModule, RoleAccessDirective, RoleClassDirective, RoleDisableDirective],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  currentUser: any = null;
  userRole: string = '';
  settingsVisibility: { [key: string]: boolean } = {};
  availableModules: string[] = [];
  editableModules: string[] = [];

  // Profile sections based on role
  profileSections: any[] = [];

  constructor(
    private authService: AuthService,
    private roleAccessService: RoleAccessService
  ) {}

  ngOnInit() {
    this.loadUserProfile();
    this.setupRoleBasedAccess();
  }

  loadUserProfile() {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.userRole = this.roleAccessService.getCurrentUserRole();
      this.setupRoleBasedAccess();
    });
  }

  setupRoleBasedAccess() {
    this.settingsVisibility = this.roleAccessService.getSettingsVisibility();
    this.availableModules = this.roleAccessService.getAvailableModules();
    this.editableModules = this.roleAccessService.getEditableModules();
    this.setupProfileSections();
  }

  setupProfileSections() {
    this.profileSections = [
      {
        id: 'basic',
        title: 'Basic Information',
        icon: 'fas fa-user',
        visible: true,
        editable: this.roleAccessService.canWrite('profile')
      },
      {
        id: 'security',
        title: 'Security Settings',
        icon: 'fas fa-shield-alt',
        visible: this.settingsVisibility['security'],
        editable: this.roleAccessService.canWrite('profile')
      },
      {
        id: 'notifications',
        title: 'Notification Preferences',
        icon: 'fas fa-bell',
        visible: this.settingsVisibility['notifications'],
        editable: this.roleAccessService.canWrite('profile')
      },
      {
        id: 'orders',
        title: 'Order History',
        icon: 'fas fa-shopping-bag',
        visible: this.settingsVisibility['orders'],
        editable: false
      },
      {
        id: 'vendor',
        title: 'Vendor Dashboard',
        icon: 'fas fa-store',
        visible: this.settingsVisibility['vendor'],
        editable: this.roleAccessService.canWrite('vendor')
      },
      {
        id: 'analytics',
        title: 'Analytics & Reports',
        icon: 'fas fa-chart-bar',
        visible: this.roleAccessService.canRead('analytics'),
        editable: this.roleAccessService.canWrite('analytics')
      },
      {
        id: 'users',
        title: 'User Management',
        icon: 'fas fa-users',
        visible: this.roleAccessService.canRead('users'),
        editable: this.roleAccessService.canWrite('users')
      },
      {
        id: 'system',
        title: 'System Settings',
        icon: 'fas fa-cogs',
        visible: this.roleAccessService.canRead('system'),
        editable: this.roleAccessService.canWrite('system')
      }
    ].filter(section => section.visible);
  }

  // Role-based utility methods
  canEditProfile(): boolean {
    return this.roleAccessService.canWrite('profile');
  }

  canViewAnalytics(): boolean {
    return this.roleAccessService.canRead('analytics');
  }

  canManageUsers(): boolean {
    return this.roleAccessService.canRead('users');
  }

  canAccessVendorFeatures(): boolean {
    return this.roleAccessService.isSeller() || this.roleAccessService.hasElevatedPrivileges();
  }

  canAccessAdminFeatures(): boolean {
    return this.roleAccessService.hasElevatedPrivileges();
  }

  getRoleDisplayName(): string {
    const roleInfo = this.roleAccessService.getRoleInfo();
    return roleInfo?.name || this.userRole;
  }

  getRoleColor(): string {
    const colors: { [key: string]: string } = {
      'buyer': '#28a745',
      'seller': '#007bff',
      'admin': '#fd7e14',
      'super_admin': '#dc3545'
    };
    return colors[this.userRole] || '#6c757d';
  }

  // Navigation methods
  navigateToSection(sectionId: string) {
    // Implement navigation logic based on section
    console.log('Navigating to section:', sectionId);
  }

  // Profile update methods
  updateProfile() {
    if (!this.canEditProfile()) {
      alert('You do not have permission to edit your profile');
      return;
    }
    // Implement profile update logic
    console.log('Updating profile...');
  }

  changePassword() {
    if (!this.canEditProfile()) {
      alert('You do not have permission to change your password');
      return;
    }
    // Implement password change logic
    console.log('Changing password...');
  }

  getSectionDescription(sectionId: string): string {
    const descriptions: { [key: string]: string } = {
      'basic': 'Manage your personal information and contact details',
      'security': 'Update password and security settings',
      'notifications': 'Configure your notification preferences',
      'orders': 'View your order history and track shipments',
      'vendor': 'Manage your seller account and business information',
      'analytics': 'View performance metrics and reports',
      'users': 'Manage user accounts and permissions',
      'system': 'Configure system-wide settings and preferences'
    };
    return descriptions[sectionId] || 'Manage this section';
  }
}
