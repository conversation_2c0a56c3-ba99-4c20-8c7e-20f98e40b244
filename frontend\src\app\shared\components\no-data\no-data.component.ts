import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-no-data',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="no-data-container" [class]="containerClass">
      <div class="no-data-content">
        <div class="no-data-icon">
          <i [class]="iconClass"></i>
        </div>
        
        <h3 class="no-data-title">{{ title }}</h3>
        <p class="no-data-message">{{ message }}</p>
        
        <div class="no-data-actions" *ngIf="showActions">
          <button 
            *ngIf="primaryAction" 
            class="btn-primary"
            (click)="onPrimaryAction()"
          >
            {{ primaryAction }}
          </button>
          
          <button 
            *ngIf="secondaryAction" 
            class="btn-secondary"
            (click)="onSecondaryAction()"
          >
            {{ secondaryAction }}
          </button>
        </div>
        
        <div class="no-data-suggestions" *ngIf="suggestions && suggestions.length > 0">
          <h4>{{ suggestionsTitle || 'Suggestions:' }}</h4>
          <ul>
            <li *ngFor="let suggestion of suggestions" (click)="onSuggestionClick(suggestion)">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .no-data-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      padding: 2rem;
      text-align: center;
    }

    .no-data-container.full-height {
      min-height: 400px;
    }

    .no-data-container.compact {
      min-height: 150px;
      padding: 1rem;
    }

    .no-data-content {
      max-width: 400px;
      width: 100%;
    }

    .no-data-icon {
      margin-bottom: 1.5rem;
    }

    .no-data-icon i {
      font-size: 4rem;
      color: #dee2e6;
      opacity: 0.8;
    }

    .no-data-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #495057;
      margin: 0 0 0.75rem 0;
    }

    .no-data-message {
      font-size: 1rem;
      color: #6c757d;
      margin: 0 0 1.5rem 0;
      line-height: 1.5;
    }

    .no-data-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 1.5rem;
    }

    .btn-primary, .btn-secondary {
      padding: 0.75rem 1.5rem;
      border-radius: 6px;
      font-weight: 500;
      cursor: pointer;
      border: none;
      transition: all 0.2s ease;
      font-size: 0.9rem;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-secondary {
      background: #f8f9fa;
      color: #6c757d;
      border: 1px solid #dee2e6;
    }

    .btn-secondary:hover {
      background: #e9ecef;
      color: #495057;
    }

    .no-data-suggestions {
      text-align: left;
    }

    .no-data-suggestions h4 {
      font-size: 1rem;
      font-weight: 600;
      color: #495057;
      margin: 0 0 0.75rem 0;
    }

    .no-data-suggestions ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .no-data-suggestions li {
      padding: 0.5rem 0.75rem;
      background: #f8f9fa;
      border-radius: 4px;
      margin-bottom: 0.5rem;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #495057;
    }

    .no-data-suggestions li:hover {
      background: #e9ecef;
      color: #007bff;
    }

    .no-data-suggestions li:last-child {
      margin-bottom: 0;
    }

    @media (max-width: 768px) {
      .no-data-container {
        padding: 1rem;
      }

      .no-data-icon i {
        font-size: 3rem;
      }

      .no-data-title {
        font-size: 1.25rem;
      }

      .no-data-message {
        font-size: 0.9rem;
      }

      .no-data-actions {
        flex-direction: column;
        align-items: center;
      }

      .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 200px;
      }
    }
  `]
})
export class NoDataComponent {
  @Input() title: string = 'No Data Available';
  @Input() message: string = 'There is no data to display at the moment.';
  @Input() iconClass: string = 'fas fa-inbox';
  @Input() containerClass: string = '';
  @Input() showActions: boolean = false;
  @Input() primaryAction?: string;
  @Input() secondaryAction?: string;
  @Input() suggestions?: string[];
  @Input() suggestionsTitle?: string;

  onPrimaryAction() {
    // Emit event or handle primary action
    console.log('Primary action clicked');
  }

  onSecondaryAction() {
    // Emit event or handle secondary action
    console.log('Secondary action clicked');
  }

  onSuggestionClick(suggestion: string) {
    // Emit event or handle suggestion click
    console.log('Suggestion clicked:', suggestion);
  }
}
