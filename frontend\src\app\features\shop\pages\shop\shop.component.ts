import { Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ProductService } from '../../../../core/services/product.service';
import { AuthService } from '../../../../core/services/auth.service';
import { CartService } from '../../../../core/services/cart.service';
import { WishlistService } from '../../../../core/services/wishlist.service';
import { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';
import { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';
import { ErrorDisplayComponent } from '../../../../shared/components/error-display/error-display.component';
import { ErrorHandlerService } from '../../../../core/services/error-handler.service';
import { LoadingService } from '../../../../core/services/loading.service';

@Component({
  selector: 'app-shop',
  standalone: true,
  imports: [CommonModule, FormsModule, NoDataComponent, LoadingSpinnerComponent, ErrorDisplayComponent],
  templateUrl: './shop.component.html',
  styleUrls: ['./shop.component.scss']
})
export class ShopComponent implements OnInit, OnDestroy {
  featuredBrands: any[] = [];
  trendingProducts: any[] = [];
  newArrivals: any[] = [];
  categories: any[] = [];
  quickLinks: any[] = [];
  searchQuery: string = '';
  loading = true;

  // Enhanced filtering and search
  selectedCategory = '';
  selectedSubcategory = '';
  selectedBrand = '';
  filterType = ''; // 'suggested', 'trending', etc.
  sortBy = 'featured';
  priceRange = { min: 0, max: 10000 };

  // Pagination
  currentPage = 1;
  itemsPerPage = 24;
  hasMore = false;

  // All products for filtering
  allProducts: any[] = [];
  filteredProducts: any[] = [];

  private subscriptions: Subscription[] = [];

  constructor(
    private productService: ProductService,
    private authService: AuthService,
    private cartService: CartService,
    private wishlistService: WishlistService,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    private errorHandlerService: ErrorHandlerService,
    private loadingService: LoadingService
  ) {}

  ngOnInit() {
    // Check for query parameters
    this.subscriptions.push(
      this.route.queryParams.subscribe(params => {
        this.searchQuery = params['q'] || '';
        this.selectedCategory = params['category'] || '';
        this.filterType = params['filter'] || '';
        this.sortBy = params['sort'] || 'featured';

        this.loadShopData();
      })
    );

  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadShopData() {
    this.loading = true;

    // If we have filters or search, load filtered products
    if (this.searchQuery || this.selectedCategory || this.filterType) {
      this.loadProductsWithFilters();
    } else {
      // Load all data in parallel for the main shop view
      Promise.all([
        this.loadFeaturedBrands(),
        this.loadTrendingProducts(),
        this.loadNewArrivals(),
        this.loadCategories(),
        this.loadQuickLinks()
      ]).finally(() => {
        this.loading = false;
      });
    }
  }

  loadFeaturedBrands() {
    this.loadingService.startLoading(LoadingService.KEYS.BRANDS_LOAD, 'Loading featured brands...');

    return this.productService.getFeaturedBrands().toPromise().then(
      (response) => {
        if (response?.success) {
          this.featuredBrands = response.data || [];
          console.log('✅ Featured brands loaded:', this.featuredBrands.length);
        } else {
          this.featuredBrands = [];
          console.warn('⚠️ Featured brands API returned unsuccessful response');
        }
      }
    ).catch(error => {
      console.error('❌ Error loading featured brands:', error);
      this.featuredBrands = [];
      this.errorHandlerService.handleError(error, 'Loading featured brands');
      console.warn('🔄 Featured brands section will show no-data state');
    }).finally(() => {
      this.loadingService.stopLoading(LoadingService.KEYS.BRANDS_LOAD);
    });
  }

  loadTrendingProducts() {
    return this.productService.getTrendingProducts(8).toPromise().then(
      (response) => {
        if (response?.success) {
          this.trendingProducts = response.data || [];
          console.log('✅ Trending products loaded:', this.trendingProducts.length);
        } else {
          this.trendingProducts = [];
          console.warn('⚠️ Trending products API returned unsuccessful response');
        }
      }
    ).catch(error => {
      console.error('❌ Error loading trending products:', error);
      this.trendingProducts = [];
      this.errorHandlerService.handleError(error, 'Loading trending products');
      console.warn('🔄 Trending products section will show no-data state');
    });
  }

  loadNewArrivals() {
    return this.productService.getNewArrivals(8).toPromise().then(
      (response) => {
        if (response?.success) {
          this.newArrivals = response.data || [];
          console.log('✅ New arrivals loaded:', this.newArrivals.length);
        } else {
          this.newArrivals = [];
          console.warn('⚠️ New arrivals API returned unsuccessful response');
        }
      }
    ).catch(error => {
      console.error('❌ Error loading new arrivals:', error);
      this.newArrivals = [];
      this.errorHandlerService.handleError(error, 'Loading new arrivals');
      console.warn('🔄 New arrivals section will show no-data state');
    });
  }

  loadCategories() {
    return this.productService.getCategories().toPromise().then(
      (response) => {
        this.categories = response?.data || [];
        console.log('✅ Categories loaded:', this.categories.length);
      }
    ).catch(error => {
      console.error('❌ Error loading categories:', error);
      this.categories = [];
    });
  }

  loadQuickLinks() {
    // Load quick navigation links from API
    return this.http.get<any>(`${environment.apiUrl}/shop/quick-links`).toPromise().then(
      (response) => {
        this.quickLinks = response?.data || response?.links || [];
        console.log('✅ Quick links loaded:', this.quickLinks.length);
      }
    ).catch(error => {
      console.error('❌ Error loading quick links:', error);
      this.errorHandlerService.handleError(error, 'Loading quick links');

      // Set empty array - let no-data component handle the empty state
      this.quickLinks = [];
      console.warn('🔄 Quick links section will show no-data state');
    });
  }

  // Product interaction methods
  likeProduct(product: any, event: Event) {
    event.stopPropagation();
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/login']);
      return;
    }

    product.isLiked = !product.isLiked;
    product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;

    this.productService.toggleProductLike(product._id).subscribe({
      next: (response) => {
        console.log('Product like updated:', response);
      },
      error: (error) => {
        console.error('Error updating product like:', error);
        product.isLiked = !product.isLiked;
        product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;
      }
    });
  }

  shareProduct(product: any, event: Event) {
    event.stopPropagation();
    const shareData = {
      title: product.name,
      text: `Check out this amazing product: ${product.name}`,
      url: `${window.location.origin}/product/${product._id}`
    };

    if (navigator.share) {
      navigator.share(shareData);
    } else {
      navigator.clipboard.writeText(shareData.url).then(() => {
        alert('Product link copied to clipboard!');
      });
    }

    this.productService.shareProduct(product._id).subscribe({
      next: (response) => {
        product.sharesCount = (product.sharesCount || 0) + 1;
      },
      error: (error) => {
        console.error('Error tracking product share:', error);
      }
    });
  }

  commentOnProduct(product: any, event: Event) {
    event.stopPropagation();
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/login']);
      return;
    }

    this.router.navigate(['/product', product._id], {
      queryParams: { action: 'comment' }
    });
  }

  addToWishlist(product: any, event: Event) {
    event.stopPropagation();
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/login']);
      return;
    }

    this.wishlistService.addToWishlist(product._id).subscribe({
      next: (response) => {
        product.isInWishlist = true;
        console.log('Product added to wishlist:', response);
      },
      error: (error) => {
        console.error('Error adding to wishlist:', error);
      }
    });
  }

  addToCart(product: any, event: Event) {
    event.stopPropagation();
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/login']);
      return;
    }

    this.cartService.addToCart(product._id, 1).subscribe({
      next: (response) => {
        console.log('Product added to cart:', response);
        alert('Product added to cart successfully!');
      },
      error: (error) => {
        console.error('Error adding to cart:', error);
      }
    });
  }

  // Navigation methods
  viewProduct(product: any) {
    this.router.navigate(['/product', product._id]);
  }

  navigateToCategory(category: any) {
    this.router.navigate(['/category', category.slug]);
  }

  search() {
    if (this.searchQuery.trim()) {
      this.updateUrlParams();
      this.loadShopData();
    }
  }

  // Enhanced filtering methods
  onCategoryChange() {
    this.selectedSubcategory = '';
    this.updateUrlParams();
    this.loadShopData();
  }

  onFilterChange() {
    this.updateUrlParams();
    this.loadShopData();
  }

  onSortChange() {
    this.updateUrlParams();
    this.loadShopData();
  }

  clearFilters() {
    this.searchQuery = '';
    this.selectedCategory = '';
    this.selectedSubcategory = '';
    this.selectedBrand = '';
    this.filterType = '';
    this.sortBy = 'featured';
    this.priceRange = { min: 0, max: 10000 };
    this.updateUrlParams();
    this.loadShopData();
  }

  private updateUrlParams() {
    const queryParams: any = {};

    if (this.searchQuery) queryParams.q = this.searchQuery;
    if (this.selectedCategory) queryParams.category = this.selectedCategory;
    if (this.filterType) queryParams.filter = this.filterType;
    if (this.sortBy !== 'featured') queryParams.sort = this.sortBy;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge'
    });
  }

  // Enhanced product loading with filtering
  loadProductsWithFilters() {
    this.loading = true;

    let endpoint = `${environment.apiUrl}/products`;

    // Use specific endpoints for filtered content
    if (this.filterType === 'suggested') {
      endpoint = `${environment.apiUrl}/products/suggested`;
    } else if (this.filterType === 'trending') {
      endpoint = `${environment.apiUrl}/products/trending`;
    }

    const params = new URLSearchParams({
      page: this.currentPage.toString(),
      limit: this.itemsPerPage.toString(),
      sort: this.sortBy
    });

    if (this.searchQuery) params.append('q', this.searchQuery);
    if (this.selectedCategory) params.append('category', this.selectedCategory);
    if (this.selectedSubcategory) params.append('subcategory', this.selectedSubcategory);
    if (this.selectedBrand) params.append('brand', this.selectedBrand);

    this.subscriptions.push(
      this.http.get<any>(`${endpoint}?${params.toString()}`).subscribe({
        next: (response) => {
          if (response.success) {
            this.allProducts = response.products || [];
            this.filteredProducts = this.allProducts;
            this.hasMore = response.pagination?.page < response.pagination?.pages;
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading filtered products:', error);
          this.loading = false;
        }
      })
    );
  }

  getProductImage(product: any): string {
    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';
  }

  getDiscountPercentage(product: any): number {
    if (!product.originalPrice || product.originalPrice <= product.price) return 0;
    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
  }
}
