{"name": "onlywomans-backend", "version": "1.0.0", "description": "Backend API for OnlyWomans - Women's Fashion E-commerce Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js"}, "keywords": ["women", "fashion", "ecommerce", "api", "nodejs", "mongodb"], "author": "OnlyWomans Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "nodemailer": "^6.9.4", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "socket.io": "^4.7.2", "stripe": "^13.5.0", "razorpay": "^2.9.2", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}