<ion-app>
  <ion-split-pane contentId="main-content">
    <!-- Side Menu -->
    <ion-menu contentId="main-content" type="overlay" class="women-menu">
      <ion-content>
        <!-- <PERSON>u Header -->
        <ion-header class="menu-header">
          <div class="user-profile">
            <div class="avatar-container">
              <img src="assets/images/default-avatar.png" alt="Profile" class="user-avatar">
              <div class="online-indicator"></div>
            </div>
            <div class="user-info">
              <h3 class="user-name">Welcome, Beautiful!</h3>
              <p class="user-subtitle">Discover your style</p>
            </div>
          </div>
        </ion-header>

        <!-- Menu Items -->
        <ion-list class="menu-list">
          <ion-menu-toggle auto-hide="false" *ngFor="let p of appPages; let i = index">
            <ion-item 
              routerDirection="root" 
              [routerLink]="[p.url]" 
              lines="none" 
              detail="false" 
              routerLinkActive="selected"
              class="menu-item">
              <ion-icon 
                aria-hidden="true" 
                slot="start" 
                [ios]="p.icon + '-outline'" 
                [md]="p.icon + '-sharp'"
                class="menu-icon">
              </ion-icon>
              <ion-label class="menu-label">{{ p.title }}</ion-label>
              <ion-icon 
                name="chevron-forward" 
                slot="end" 
                class="chevron-icon">
              </ion-icon>
            </ion-item>
          </ion-menu-toggle>
        </ion-list>

        <!-- Special Categories -->
        <div class="menu-section">
          <ion-item-divider class="section-divider">
            <ion-label class="section-title">
              <ion-icon name="sparkles" class="section-icon"></ion-icon>
              Special Collections
            </ion-label>
          </ion-item-divider>
          
          <ion-menu-toggle auto-hide="false" *ngFor="let label of labels; let i = index">
            <ion-item 
              lines="none" 
              detail="false"
              class="menu-item special-item">
              <ion-icon 
                aria-hidden="true" 
                slot="start" 
                name="star"
                class="menu-icon special-icon">
              </ion-icon>
              <ion-label class="menu-label">{{ label }}</ion-label>
              <ion-badge 
                slot="end" 
                color="primary" 
                class="item-badge">
                {{ i + 1 }}
              </ion-badge>
            </ion-item>
          </ion-menu-toggle>
        </div>

        <!-- Menu Footer -->
        <div class="menu-footer">
          <ion-item lines="none" class="footer-item">
            <ion-icon name="settings" slot="start" class="footer-icon"></ion-icon>
            <ion-label>Settings</ion-label>
          </ion-item>
          
          <ion-item lines="none" class="footer-item">
            <ion-icon name="help-circle" slot="start" class="footer-icon"></ion-icon>
            <ion-label>Help & Support</ion-label>
          </ion-item>
          
          <ion-item lines="none" class="footer-item logout-item">
            <ion-icon name="log-out" slot="start" class="footer-icon"></ion-icon>
            <ion-label>Logout</ion-label>
          </ion-item>
        </div>
      </ion-content>
    </ion-menu>

    <!-- Main Content -->
    <ion-router-outlet id="main-content"></ion-router-outlet>
  </ion-split-pane>
</ion-app>
