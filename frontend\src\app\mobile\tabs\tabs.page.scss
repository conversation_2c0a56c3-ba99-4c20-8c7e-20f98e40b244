ion-tab-bar {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
  --color-selected: var(--ion-color-secondary);
  border-top: 1px solid var(--ion-color-light-shade);
  padding-bottom: env(safe-area-inset-bottom);
}

ion-tab-button {
  --color: rgba(255, 255, 255, 0.7);
  --color-selected: #ffffff;
  font-size: 12px;
  
  ion-icon {
    font-size: 24px;
    margin-bottom: 2px;
  }
  
  ion-label {
    font-weight: 500;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  ion-badge {
    position: absolute;
    top: 4px;
    right: 8px;
    min-width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: bold;
  }
}

// Active tab styling
ion-tab-button.tab-selected {
  --color: #ffffff;
  
  ion-icon {
    transform: scale(1.1);
    transition: transform 0.2s ease;
  }
}

// Smooth transitions
ion-tab-button {
  transition: all 0.2s ease;
  
  &:hover {
    --color: rgba(255, 255, 255, 0.9);
  }
}
