import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { ShopComponent } from './shop.component';
import { ProductService } from '../../../../core/services/product.service';
import { AuthService } from '../../../../core/services/auth.service';
import { CartService } from '../../../../core/services/cart.service';
import { WishlistService } from '../../../../core/services/wishlist.service';
import { ErrorHandlerService } from '../../../../core/services/error-handler.service';
import { LoadingService } from '../../../../core/services/loading.service';
import { TestUtilsService } from '../../../../testing/test-utils.service';

describe('ShopComponent', () => {
  let component: ShopComponent;
  let fixture: ComponentFixture<ShopComponent>;
  let testUtils: TestUtilsService;
  let mockProductService: jasmine.SpyObj<ProductService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockCartService: jasmine.SpyObj<CartService>;
  let mockWishlistService: jasmine.SpyObj<WishlistService>;
  let mockErrorHandlerService: jasmine.SpyObj<ErrorHandlerService>;
  let mockLoadingService: jasmine.SpyObj<LoadingService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    testUtils = new TestUtilsService();
    
    // Create mock services
    mockProductService = jasmine.createSpyObj('ProductService', [
      'getProducts', 'getFeaturedBrands', 'getTrendingProducts', 
      'getNewArrivals', 'getCategories'
    ]);
    mockAuthService = testUtils.createMockAuthService(testUtils.generateMockUser());
    mockCartService = testUtils.createMockCartService();
    mockWishlistService = jasmine.createSpyObj('WishlistService', ['addToWishlist', 'removeFromWishlist']);
    mockErrorHandlerService = testUtils.createMockErrorHandlerService();
    mockLoadingService = testUtils.createMockLoadingService();
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    
    mockActivatedRoute = {
      queryParams: of({}),
      params: of({})
    };

    // Setup default service responses
    mockProductService.getFeaturedBrands.and.returnValue(of({ 
      success: true, 
      data: [
        { id: '1', name: 'Nike', logo: 'nike.png', isPopular: true },
        { id: '2', name: 'Adidas', logo: 'adidas.png', isPopular: true }
      ] 
    }));
    
    mockProductService.getTrendingProducts.and.returnValue(of({ 
      success: true, 
      data: [testUtils.generateMockProduct({ isTrending: true })] 
    }));
    
    mockProductService.getNewArrivals.and.returnValue(of({ 
      success: true, 
      data: [testUtils.generateMockProduct({ isNew: true })] 
    }));
    
    mockProductService.getCategories.and.returnValue(of({ 
      success: true, 
      data: [{ id: '1', name: 'Electronics', slug: 'electronics' }] 
    }));

    await TestBed.configureTestingModule({
      imports: [ShopComponent, HttpClientTestingModule],
      providers: [
        { provide: ProductService, useValue: mockProductService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: CartService, useValue: mockCartService },
        { provide: WishlistService, useValue: mockWishlistService },
        { provide: ErrorHandlerService, useValue: mockErrorHandlerService },
        { provide: LoadingService, useValue: mockLoadingService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ShopComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.featuredBrands).toEqual([]);
      expect(component.trendingProducts).toEqual([]);
      expect(component.newArrivals).toEqual([]);
      expect(component.categories).toEqual([]);
      expect(component.loading).toBe(true);
    });

    it('should load shop data on init', async () => {
      fixture.detectChanges();
      
      await fixture.whenStable();
      
      expect(mockProductService.getFeaturedBrands).toHaveBeenCalled();
      expect(mockProductService.getTrendingProducts).toHaveBeenCalled();
      expect(mockProductService.getNewArrivals).toHaveBeenCalled();
      expect(mockProductService.getCategories).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should load featured brands successfully', async () => {
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(component.featuredBrands.length).toBe(2);
      expect(component.featuredBrands[0].name).toBe('Nike');
      expect(component.featuredBrands[1].name).toBe('Adidas');
    });

    it('should handle featured brands loading error', async () => {
      mockProductService.getFeaturedBrands.and.returnValue(
        throwError(() => testUtils.createHttpError(500, 'Server error'))
      );
      
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(component.featuredBrands).toEqual([]);
      expect(mockErrorHandlerService.handleError).toHaveBeenCalled();
    });

    it('should load trending products successfully', async () => {
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(component.trendingProducts.length).toBe(1);
      expect(component.trendingProducts[0].isTrending).toBe(true);
    });

    it('should load new arrivals successfully', async () => {
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(component.newArrivals.length).toBe(1);
      expect(component.newArrivals[0].isNew).toBe(true);
    });

    it('should set loading to false after all data is loaded', async () => {
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(component.loading).toBe(false);
    });
  });

  describe('Loading States', () => {
    it('should start loading service for brands', async () => {
      fixture.detectChanges();
      
      expect(mockLoadingService.startLoading).toHaveBeenCalledWith(
        LoadingService.KEYS.BRANDS_LOAD, 
        'Loading featured brands...'
      );
    });

    it('should stop loading service after brands are loaded', async () => {
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(mockLoadingService.stopLoading).toHaveBeenCalledWith(
        LoadingService.KEYS.BRANDS_LOAD
      );
    });
  });

  describe('User Interactions', () => {
    beforeEach(async () => {
      fixture.detectChanges();
      await fixture.whenStable();
    });

    it('should add product to cart', () => {
      const mockProduct = testUtils.generateMockProduct();
      const mockEvent = new Event('click');
      
      component.addToCart(mockProduct, mockEvent);
      
      expect(mockCartService.addToCart).toHaveBeenCalledWith(mockProduct);
    });

    it('should add product to wishlist', () => {
      const mockProduct = testUtils.generateMockProduct();
      const mockEvent = new Event('click');
      
      component.likeProduct(mockProduct, mockEvent);
      
      expect(mockWishlistService.addToWishlist).toHaveBeenCalledWith(mockProduct._id);
    });

    it('should navigate to product details', () => {
      const mockProduct = testUtils.generateMockProduct();
      
      component.viewProduct(mockProduct);
      
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/product', mockProduct._id]);
    });
  });

  describe('Template Rendering', () => {
    beforeEach(async () => {
      fixture.detectChanges();
      await fixture.whenStable();
    });

    it('should display featured brands section', () => {
      testUtils.expectElementToExist(fixture, '.featured-brands-section');
      testUtils.expectElementToContainText(fixture, '.section-title', 'Featured Brands');
    });

    it('should display trending products section', () => {
      testUtils.expectElementToExist(fixture, '.trending-section');
      testUtils.expectElementToContainText(fixture, '.section-title', 'Trending Now');
    });

    it('should display new arrivals section', () => {
      testUtils.expectElementToExist(fixture, '.new-arrivals-section');
      testUtils.expectElementToContainText(fixture, '.section-title', 'New Arrivals');
    });

    it('should show no-data component when no brands are available', () => {
      component.featuredBrands = [];
      component.loading = false;
      fixture.detectChanges();
      
      testUtils.expectElementToExist(fixture, 'app-no-data');
    });

    it('should show loading spinner when loading', () => {
      component.loading = true;
      fixture.detectChanges();
      
      testUtils.expectElementToExist(fixture, 'app-loading-spinner');
    });

    it('should show error display component', () => {
      testUtils.expectElementToExist(fixture, 'app-error-display');
    });
  });

  describe('Query Parameters', () => {
    it('should handle search query parameter', () => {
      mockActivatedRoute.queryParams = of({ q: 'test search' });
      
      fixture.detectChanges();
      
      expect(component.searchQuery).toBe('test search');
    });

    it('should handle category parameter', () => {
      mockActivatedRoute.queryParams = of({ category: 'electronics' });
      
      fixture.detectChanges();
      
      expect(component.selectedCategory).toBe('electronics');
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockProductService.getFeaturedBrands.and.returnValue(
        testUtils.createNetworkError()
      );
      
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(mockErrorHandlerService.handleError).toHaveBeenCalled();
      expect(component.featuredBrands).toEqual([]);
    });

    it('should handle server errors gracefully', async () => {
      mockProductService.getTrendingProducts.and.returnValue(
        testUtils.createServerError()
      );
      
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(mockErrorHandlerService.handleError).toHaveBeenCalled();
      expect(component.trendingProducts).toEqual([]);
    });
  });

  describe('Accessibility', () => {
    beforeEach(async () => {
      fixture.detectChanges();
      await fixture.whenStable();
    });

    it('should have proper ARIA labels', () => {
      const brandCards = testUtils.getAllElementsBySelector(fixture, '.brand-card');
      brandCards.forEach(card => {
        expect(card.nativeElement.getAttribute('role')).toBeTruthy();
      });
    });

    it('should have proper heading hierarchy', () => {
      const headings = testUtils.getAllElementsBySelector(fixture, 'h1, h2, h3, h4, h5, h6');
      expect(headings.length).toBeGreaterThan(0);
    });
  });
});
