import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'ow-home',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              <span class="title-line">Fashion That</span>
              <span class="title-line highlight">Celebrates You</span>
            </h1>
            <p class="hero-subtitle">
              Discover curated collections designed exclusively for women. 
              From ethnic elegance to western chic, find your perfect style.
            </p>
            <div class="hero-actions">
              <button class="btn btn-primary btn-lg" routerLink="/shop">
                <span>Shop Now</span>
                <i class="icon">🛍️</i>
              </button>
              <button class="btn btn-outline btn-lg" routerLink="/categories">
                <span>Explore Categories</span>
                <i class="icon">✨</i>
              </button>
            </div>
          </div>
          <div class="hero-image">
            <div class="image-container">
              <img src="assets/images/hero-woman.jpg" alt="Elegant Woman in Fashion" class="main-image">
              <div class="floating-elements">
                <div class="floating-item item-1">👗</div>
                <div class="floating-item item-2">💄</div>
                <div class="floating-item item-3">👠</div>
                <div class="floating-item item-4">💎</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Categories Section -->
      <section class="categories-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">Shop by Category</h2>
            <p class="section-subtitle">Explore our curated collections for every occasion</p>
          </div>
          
          <div class="categories-grid">
            <div *ngFor="let category of featuredCategories" 
                 class="category-card"
                 [routerLink]="['/categories', category.slug]">
              <div class="category-image">
                <img [src]="category.image" [alt]="category.name">
                <div class="category-overlay">
                  <span class="category-icon">{{ category.icon }}</span>
                </div>
              </div>
              <div class="category-info">
                <h3 class="category-name">{{ category.name }}</h3>
                <p class="category-description">{{ category.description }}</p>
                <span class="category-count">{{ category.productCount }}+ items</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Featured Products Section -->
      <section class="featured-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">Trending Now</h2>
            <p class="section-subtitle">Handpicked favorites that are flying off the shelves</p>
          </div>
          
          <div class="products-grid">
            <div *ngFor="let product of featuredProducts" class="product-card">
              <div class="product-image">
                <img [src]="product.image" [alt]="product.name">
                <div class="product-badges">
                  <span *ngIf="product.isNew" class="badge badge-new">New</span>
                  <span *ngIf="product.discount" class="badge badge-sale">{{ product.discount }}% Off</span>
                </div>
                <button class="wishlist-btn" [class.active]="product.isWishlisted">
                  <i class="heart-icon">{{ product.isWishlisted ? '❤️' : '🤍' }}</i>
                </button>
              </div>
              <div class="product-info">
                <span class="product-brand">{{ product.brand }}</span>
                <h3 class="product-name">{{ product.name }}</h3>
                <div class="product-price">
                  <span class="current-price">₹{{ product.price }}</span>
                  <span *ngIf="product.originalPrice" class="original-price">₹{{ product.originalPrice }}</span>
                </div>
                <div class="product-rating">
                  <div class="stars">
                    <span *ngFor="let star of getStars(product.rating)">{{ star }}</span>
                  </div>
                  <span class="rating-text">({{ product.reviewCount }})</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section-footer">
            <button class="btn btn-outline btn-lg" routerLink="/shop">
              View All Products
            </button>
          </div>
        </div>
      </section>

      <!-- Special Offers Section -->
      <section class="offers-section">
        <div class="container">
          <div class="offers-grid">
            <div class="offer-card offer-primary">
              <div class="offer-content">
                <h3 class="offer-title">New User Special</h3>
                <p class="offer-description">Get 30% off on your first order</p>
                <button class="btn btn-primary">Claim Now</button>
              </div>
              <div class="offer-icon">🎁</div>
            </div>
            
            <div class="offer-card offer-secondary">
              <div class="offer-content">
                <h3 class="offer-title">Free Shipping</h3>
                <p class="offer-description">On orders above ₹999</p>
                <button class="btn btn-secondary">Shop Now</button>
              </div>
              <div class="offer-icon">🚚</div>
            </div>
            
            <div class="offer-card offer-accent">
              <div class="offer-content">
                <h3 class="offer-title">Beauty Box</h3>
                <p class="offer-description">Curated makeup essentials</p>
                <button class="btn btn-outline">Explore</button>
              </div>
              <div class="offer-icon">💄</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Newsletter Section -->
      <section class="newsletter-section">
        <div class="container">
          <div class="newsletter-content">
            <div class="newsletter-text">
              <h2 class="newsletter-title">Stay in Style</h2>
              <p class="newsletter-subtitle">
                Get the latest fashion trends, beauty tips, and exclusive offers delivered to your inbox.
              </p>
            </div>
            <div class="newsletter-form">
              <div class="form-group">
                <input type="email" placeholder="Enter your email address" class="email-input">
                <button class="btn btn-primary">Subscribe</button>
              </div>
              <p class="newsletter-note">
                Join 50,000+ women who trust us for their fashion needs.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  `,
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  
  featuredCategories = [
    {
      name: 'Ethnic Wear',
      slug: 'ethnic-wear',
      description: 'Traditional elegance for every occasion',
      image: 'assets/images/categories/ethnic.jpg',
      icon: '🥻',
      productCount: 1250
    },
    {
      name: 'Western Wear',
      slug: 'western-wear',
      description: 'Contemporary styles for modern women',
      image: 'assets/images/categories/western.jpg',
      icon: '👗',
      productCount: 980
    },
    {
      name: 'Beauty & Makeup',
      slug: 'beauty-makeup',
      description: 'Enhance your natural beauty',
      image: 'assets/images/categories/beauty.jpg',
      icon: '💄',
      productCount: 750
    },
    {
      name: 'Accessories',
      slug: 'accessories',
      description: 'Complete your look with style',
      image: 'assets/images/categories/accessories.jpg',
      icon: '👜',
      productCount: 650
    },
    {
      name: 'Footwear',
      slug: 'footwear',
      description: 'Step out in confidence',
      image: 'assets/images/categories/footwear.jpg',
      icon: '👠',
      productCount: 420
    },
    {
      name: 'Jewelry',
      slug: 'jewelry',
      description: 'Sparkle with every piece',
      image: 'assets/images/categories/jewelry.jpg',
      icon: '💎',
      productCount: 380
    }
  ];

  featuredProducts = [
    {
      id: 1,
      name: 'Floral Maxi Dress',
      brand: 'StyleVogue',
      price: 2499,
      originalPrice: 3499,
      discount: 29,
      image: 'assets/images/products/dress1.jpg',
      rating: 4.5,
      reviewCount: 128,
      isNew: true,
      isWishlisted: false
    },
    {
      id: 2,
      name: 'Silk Saree with Blouse',
      brand: 'EthnicElegance',
      price: 4999,
      originalPrice: 6999,
      discount: 29,
      image: 'assets/images/products/saree1.jpg',
      rating: 4.8,
      reviewCount: 95,
      isNew: false,
      isWishlisted: true
    },
    {
      id: 3,
      name: 'Designer Handbag',
      brand: 'LuxeCollection',
      price: 1899,
      originalPrice: null,
      discount: null,
      image: 'assets/images/products/bag1.jpg',
      rating: 4.3,
      reviewCount: 67,
      isNew: true,
      isWishlisted: false
    },
    {
      id: 4,
      name: 'Makeup Palette Set',
      brand: 'GlamBeauty',
      price: 1299,
      originalPrice: 1799,
      discount: 28,
      image: 'assets/images/products/makeup1.jpg',
      rating: 4.6,
      reviewCount: 203,
      isNew: false,
      isWishlisted: false
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Initialize component
  }

  getStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push('⭐');
    }
    
    if (hasHalfStar) {
      stars.push('⭐');
    }
    
    while (stars.length < 5) {
      stars.push('☆');
    }
    
    return stars;
  }
}
