const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration for women's app
app.use(cors({
  origin: [
    'http://localhost:4200',
    'http://localhost:8100',
    'https://onlywomans.com',
    'https://app.onlywomans.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files for women's theme
app.use('/uploads', express.static('uploads'));
app.use('/assets', express.static('assets'));

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/onlywomans', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`🌸 MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('❌ Database connection error:', error);
    process.exit(1);
  }
};

// Connect to database
connectDB();

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/products', require('./routes/products'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/cart', require('./routes/cart'));
app.use('/api/orders', require('./routes/orders'));
app.use('/api/wishlist', require('./routes/wishlist'));
app.use('/api/reviews', require('./routes/reviews'));
app.use('/api/users', require('./routes/users'));
app.use('/api/brands', require('./routes/brands'));
app.use('/api/search', require('./routes/search'));
app.use('/api/recommendations', require('./routes/recommendations'));
app.use('/api/analytics', require('./routes/analytics'));
app.use('/api/social', require('./routes/social'));
app.use('/api/beauty', require('./routes/beauty'));
app.use('/api/style', require('./routes/style'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: '🌸 OnlyWomans API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Welcome endpoint with women's theme
app.get('/', (req, res) => {
  res.json({
    message: '🌸 Welcome to OnlyWomans API - Fashion for Every Woman 💃',
    version: '1.0.0',
    features: [
      '👗 Women\'s Fashion Catalog',
      '💄 Beauty & Makeup Products',
      '👠 Footwear & Accessories',
      '💎 Jewelry Collection',
      '🛍️ Smart Shopping Cart',
      '❤️ Wishlist & Favorites',
      '⭐ Reviews & Ratings',
      '🎯 Personalized Recommendations',
      '📱 Mobile-First Design',
      '🔐 Secure Payments'
    ],
    endpoints: {
      auth: '/api/auth',
      products: '/api/products',
      cart: '/api/cart',
      orders: '/api/orders',
      wishlist: '/api/wishlist',
      beauty: '/api/beauty',
      style: '/api/style'
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Error:', err.stack);
  
  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(e => e.message);
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      errors
    });
  }
  
  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(400).json({
      success: false,
      message: `${field} already exists`
    });
  }
  
  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired'
    });
  }
  
  // Default error
  res.status(err.statusCode || 500).json({
    success: false,
    message: err.message || 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found 🌸'
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🌸 SIGTERM received, shutting down gracefully');
  mongoose.connection.close(() => {
    console.log('🌸 Database connection closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🌸 SIGINT received, shutting down gracefully');
  mongoose.connection.close(() => {
    console.log('🌸 Database connection closed');
    process.exit(0);
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`
  🌸✨ OnlyWomans API Server ✨🌸
  
  🚀 Server running on port ${PORT}
  🌍 Environment: ${process.env.NODE_ENV || 'development'}
  📱 Ready for web and mobile apps
  💖 Designed exclusively for women
  
  API Endpoints:
  🔐 Auth: http://localhost:${PORT}/api/auth
  👗 Products: http://localhost:${PORT}/api/products
  🛍️ Cart: http://localhost:${PORT}/api/cart
  ❤️ Wishlist: http://localhost:${PORT}/api/wishlist
  💄 Beauty: http://localhost:${PORT}/api/beauty
  
  Happy coding! 💃✨
  `);
});

module.exports = app;
