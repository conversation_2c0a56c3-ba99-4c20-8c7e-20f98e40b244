// OnlyWomans Mobile App - Women's Theme Styles

// Menu Styling
.women-menu {
  --background: linear-gradient(135deg, #fdf2f8 0%, #faf5ff 100%);
  
  ion-content {
    --background: transparent;
  }
}

// Menu Header
.menu-header {
  background: linear-gradient(135deg, #ec4899, #a855f7);
  padding: 2rem 1.5rem 1.5rem;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-bottom: 1rem;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.avatar-container {
  position: relative;
  
  .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
  }
  
  .online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    background: #10b981;
    border: 2px solid white;
    border-radius: 50%;
  }
}

.user-info {
  flex: 1;
  
  .user-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: white;
  }
  
  .user-subtitle {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
    color: white;
  }
}

// Menu List
.menu-list {
  background: transparent;
  padding: 0 0.5rem;
}

.menu-item {
  --background: transparent;
  --color: #374151;
  --border-radius: 12px;
  --padding-start: 1rem;
  --padding-end: 1rem;
  --inner-padding-end: 0;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    --background: rgba(236, 72, 153, 0.1);
  }
  
  &.selected {
    --background: linear-gradient(135deg, rgba(236, 72, 153, 0.15), rgba(168, 85, 247, 0.15));
    --color: #ec4899;
    
    .menu-icon {
      color: #ec4899;
    }
    
    .chevron-icon {
      color: #ec4899;
    }
  }
}

.menu-icon {
  font-size: 1.3rem;
  margin-right: 1rem;
  color: #6b7280;
  transition: color 0.3s ease;
}

.menu-label {
  font-weight: 500;
  font-size: 1rem;
}

.chevron-icon {
  font-size: 1rem;
  color: #9ca3af;
  transition: color 0.3s ease;
}

// Special Items
.special-item {
  .special-icon {
    color: #f59e0b;
  }
}

.item-badge {
  --background: #ec4899;
  --color: white;
  font-size: 0.75rem;
  font-weight: 600;
}

// Menu Sections
.menu-section {
  margin-top: 1.5rem;
  padding: 0 0.5rem;
}

.section-divider {
  --background: transparent;
  --color: #6b7280;
  margin-bottom: 0.5rem;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .section-icon {
    color: #f59e0b;
    font-size: 1rem;
  }
}

// Menu Footer
.menu-footer {
  margin-top: auto;
  padding: 1rem 0.5rem 2rem;
  border-top: 1px solid rgba(236, 72, 153, 0.1);
}

.footer-item {
  --background: transparent;
  --color: #6b7280;
  --border-radius: 8px;
  --padding-start: 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  
  &:hover {
    --background: rgba(107, 114, 128, 0.1);
  }
  
  &.logout-item {
    --color: #ef4444;
    
    .footer-icon {
      color: #ef4444;
    }
    
    &:hover {
      --background: rgba(239, 68, 68, 0.1);
    }
  }
}

.footer-icon {
  font-size: 1.1rem;
  margin-right: 0.75rem;
  color: #9ca3af;
}

// Responsive Design
@media (max-width: 768px) {
  .menu-header {
    padding: 1.5rem 1rem 1rem;
  }
  
  .user-profile {
    gap: 0.75rem;
  }
  
  .user-avatar {
    width: 50px;
    height: 50px;
  }
  
  .user-name {
    font-size: 1.1rem;
  }
  
  .user-subtitle {
    font-size: 0.8rem;
  }
}

// Dark Mode Support
@media (prefers-color-scheme: dark) {
  .women-menu {
    --background: linear-gradient(135deg, #1f1f23 0%, #2d1b69 100%);
  }
  
  .menu-item {
    --color: #e5e7eb;
    
    &:hover {
      --background: rgba(236, 72, 153, 0.2);
    }
    
    &.selected {
      --background: linear-gradient(135deg, rgba(236, 72, 153, 0.25), rgba(168, 85, 247, 0.25));
      --color: #f472b6;
    }
  }
  
  .menu-icon {
    color: #9ca3af;
  }
  
  .chevron-icon {
    color: #6b7280;
  }
  
  .section-divider {
    --color: #9ca3af;
  }
  
  .footer-item {
    --color: #9ca3af;
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// Custom Scrollbar for Menu
.women-menu ion-content {
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(236, 72, 153, 0.1);
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(236, 72, 153, 0.3);
    border-radius: 2px;
    
    &:hover {
      background: rgba(236, 72, 153, 0.5);
    }
  }
}
