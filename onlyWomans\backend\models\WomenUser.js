const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const womenUserSchema = new mongoose.Schema({
  // Basic Information
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  
  // Profile Information
  profile: {
    avatar: {
      type: String,
      default: 'https://via.placeholder.com/150/FF69B4/FFFFFF?text=👩'
    },
    dateOfBirth: Date,
    bio: {
      type: String,
      maxlength: 500
    },
    
    // Women-specific preferences
    style: {
      type: String,
      enum: ['Ethnic', 'Western', 'Fusion', 'Minimalist', 'Boho', 'Classic', 'Trendy', 'Casual', 'Formal']
    },
    favoriteColors: [String],
    preferredSizes: {
      clothing: { type: String, enum: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'] },
      footwear: { type: String },
      bra: { type: String },
      ring: { type: String }
    },
    bodyType: {
      type: String,
      enum: ['Pear', 'Apple', 'Hourglass', 'Rectangle', 'Inverted Triangle', 'Plus Size']
    },
    skinTone: {
      type: String,
      enum: ['Fair', 'Medium', 'Olive', 'Dark', 'Deep']
    },
    
    // Lifestyle preferences
    lifestyle: {
      workType: { type: String, enum: ['Office', 'Work from Home', 'Student', 'Homemaker', 'Entrepreneur', 'Other'] },
      socialActivity: { type: String, enum: ['High', 'Medium', 'Low'] },
      fitnessLevel: { type: String, enum: ['Beginner', 'Intermediate', 'Advanced', 'Not Active'] }
    }
  },
  
  // Shopping Preferences
  preferences: {
    categories: [{
      type: String,
      enum: [
        'ethnic-wear', 'western-wear', 'party-wear', 'casual-wear', 'work-wear',
        'lingerie', 'sleepwear', 'maternity', 'plus-size', 'accessories',
        'footwear', 'bags-purses', 'jewelry', 'beauty-makeup', 'skincare'
      ]
    }],
    brands: [String],
    priceRange: {
      min: { type: Number, default: 0 },
      max: { type: Number, default: 10000 }
    },
    notifications: {
      newArrivals: { type: Boolean, default: true },
      sales: { type: Boolean, default: true },
      recommendations: { type: Boolean, default: true },
      orderUpdates: { type: Boolean, default: true },
      wishlistReminders: { type: Boolean, default: false }
    }
  },
  
  // Addresses
  addresses: [{
    type: {
      type: String,
      enum: ['Home', 'Office', 'Other'],
      default: 'Home'
    },
    firstName: String,
    lastName: String,
    phone: String,
    addressLine1: { type: String, required: true },
    addressLine2: String,
    city: { type: String, required: true },
    state: { type: String, required: true },
    pincode: { type: String, required: true },
    country: { type: String, default: 'India' },
    landmark: String,
    isDefault: { type: Boolean, default: false }
  }],
  
  // Role and Status
  role: {
    type: String,
    enum: ['customer', 'seller', 'admin', 'super_admin'],
    default: 'customer'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  
  // Seller Information (if applicable)
  sellerInfo: {
    businessName: String,
    businessType: {
      type: String,
      enum: ['Individual', 'Partnership', 'Private Limited', 'LLP', 'Other']
    },
    gstNumber: String,
    panNumber: String,
    bankDetails: {
      accountNumber: String,
      ifscCode: String,
      accountHolderName: String,
      bankName: String
    },
    isApproved: { type: Boolean, default: false },
    approvedAt: Date,
    documents: [{
      type: String,
      url: String,
      verified: { type: Boolean, default: false }
    }]
  },
  
  // Social Features
  social: {
    followers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'WomenUser' }],
    following: [{ type: mongoose.Schema.Types.ObjectId, ref: 'WomenUser' }],
    posts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'WomenPost' }],
    isInfluencer: { type: Boolean, default: false },
    influencerTier: {
      type: String,
      enum: ['Micro', 'Macro', 'Celebrity'],
      default: 'Micro'
    }
  },
  
  // Shopping Analytics
  analytics: {
    totalOrders: { type: Number, default: 0 },
    totalSpent: { type: Number, default: 0 },
    averageOrderValue: { type: Number, default: 0 },
    favoriteCategory: String,
    lastOrderDate: Date,
    loyaltyPoints: { type: Number, default: 0 },
    membershipTier: {
      type: String,
      enum: ['Bronze', 'Silver', 'Gold', 'Platinum'],
      default: 'Bronze'
    }
  },
  
  // Security
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: Date,
  passwordResetToken: String,
  passwordResetExpires: Date,
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  
  // Timestamps
  lastLogin: Date,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.passwordResetToken;
      delete ret.emailVerificationToken;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes
womenUserSchema.index({ email: 1 });
womenUserSchema.index({ phone: 1 });
womenUserSchema.index({ 'profile.style': 1 });
womenUserSchema.index({ 'preferences.categories': 1 });
womenUserSchema.index({ role: 1 });
womenUserSchema.index({ isActive: 1 });

// Virtuals
womenUserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

womenUserSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

womenUserSchema.virtual('followersCount').get(function() {
  return this.social.followers.length;
});

womenUserSchema.virtual('followingCount').get(function() {
  return this.social.following.length;
});

// Pre-save middleware
womenUserSchema.pre('save', async function(next) {
  // Hash password if modified
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 12);
  }
  
  // Update timestamp
  this.updatedAt = Date.now();
  
  // Ensure only one default address
  if (this.isModified('addresses')) {
    const defaultAddresses = this.addresses.filter(addr => addr.isDefault);
    if (defaultAddresses.length > 1) {
      this.addresses.forEach((addr, index) => {
        if (index > 0) addr.isDefault = false;
      });
    }
  }
  
  next();
});

// Methods
womenUserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

womenUserSchema.methods.incrementLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

womenUserSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

womenUserSchema.methods.addToWishlist = function(productId) {
  if (!this.wishlist.includes(productId)) {
    this.wishlist.push(productId);
    return this.save();
  }
  return Promise.resolve(this);
};

womenUserSchema.methods.removeFromWishlist = function(productId) {
  this.wishlist = this.wishlist.filter(id => !id.equals(productId));
  return this.save();
};

womenUserSchema.methods.updateMembershipTier = function() {
  const spent = this.analytics.totalSpent;
  let tier = 'Bronze';
  
  if (spent >= 100000) tier = 'Platinum';
  else if (spent >= 50000) tier = 'Gold';
  else if (spent >= 20000) tier = 'Silver';
  
  this.analytics.membershipTier = tier;
  return this.save();
};

womenUserSchema.methods.addLoyaltyPoints = function(points) {
  this.analytics.loyaltyPoints += points;
  return this.save();
};

womenUserSchema.methods.getDefaultAddress = function() {
  return this.addresses.find(addr => addr.isDefault) || this.addresses[0];
};

module.exports = mongoose.model('WomenUser', womenUserSchema);
