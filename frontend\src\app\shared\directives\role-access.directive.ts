import { Directive, Input, TemplateRef, ViewContainerRef, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { RoleAccessService } from '../../core/services/role-access.service';
import { AuthService } from '../../core/services/auth.service';

@Directive({
  selector: '[appRoleAccess]',
  standalone: true
})
export class RoleAccessDirective implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();
  private hasView = false;

  @Input() set appRoleAccess(permission: string) {
    this.checkPermission(permission);
  }

  @Input() appRoleAccessElse?: TemplateRef<any>;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private roleAccessService: RoleAccessService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    // Subscribe to auth changes to update permissions dynamically
    this.subscription.add(
      this.authService.currentUser$.subscribe(() => {
        // Re-check permission when user changes
        const permission = this.templateRef.elementRef?.nativeElement?.getAttribute('appRoleAccess');
        if (permission) {
          this.checkPermission(permission);
        }
      })
    );
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private checkPermission(permission: string) {
    const hasPermission = this.evaluatePermission(permission);
    
    if (hasPermission && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasPermission && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
      
      // Show else template if provided
      if (this.appRoleAccessElse) {
        this.viewContainer.createEmbeddedView(this.appRoleAccessElse);
      }
    }
  }

  private evaluatePermission(permission: string): boolean {
    if (!permission) return true;

    // Handle different permission formats
    if (permission.includes(':')) {
      // Format: "module:action" (e.g., "products:write")
      const [module, action] = permission.split(':');
      return this.roleAccessService.hasPermission(module, action as any);
    } else if (permission.startsWith('role:')) {
      // Format: "role:roleName" (e.g., "role:admin")
      const roleName = permission.replace('role:', '');
      return this.roleAccessService.getCurrentUserRole() === roleName;
    } else if (permission.startsWith('level:')) {
      // Format: "level:number" (e.g., "level:3")
      const level = parseInt(permission.replace('level:', ''));
      return this.roleAccessService.hasRoleLevel(level);
    } else {
      // Assume it's a module name for read access
      return this.roleAccessService.canRead(permission);
    }
  }
}

// Additional directive for role-based styling
@Directive({
  selector: '[appRoleClass]',
  standalone: true
})
export class RoleClassDirective implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  @Input() set appRoleClass(config: { [role: string]: string }) {
    this.applyRoleClass(config);
  }

  constructor(
    private roleAccessService: RoleAccessService,
    private authService: AuthService,
    private elementRef: any
  ) {}

  ngOnInit() {
    this.subscription.add(
      this.authService.currentUser$.subscribe(() => {
        // Re-apply classes when user changes
        const config = this.elementRef.nativeElement?.getAttribute('appRoleClass');
        if (config) {
          this.applyRoleClass(JSON.parse(config));
        }
      })
    );
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private applyRoleClass(config: { [role: string]: string }) {
    const currentRole = this.roleAccessService.getCurrentUserRole();
    const element = this.elementRef.nativeElement;

    // Remove all role classes first
    Object.values(config).forEach(className => {
      element.classList.remove(className);
    });

    // Add class for current role
    if (config[currentRole]) {
      element.classList.add(config[currentRole]);
    }
  }
}

// Directive for disabling elements based on permissions
@Directive({
  selector: '[appRoleDisable]',
  standalone: true
})
export class RoleDisableDirective implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  @Input() set appRoleDisable(permission: string) {
    this.checkAndDisable(permission);
  }

  constructor(
    private roleAccessService: RoleAccessService,
    private authService: AuthService,
    private elementRef: any
  ) {}

  ngOnInit() {
    this.subscription.add(
      this.authService.currentUser$.subscribe(() => {
        const permission = this.elementRef.nativeElement?.getAttribute('appRoleDisable');
        if (permission) {
          this.checkAndDisable(permission);
        }
      })
    );
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private checkAndDisable(permission: string) {
    const hasPermission = this.evaluatePermission(permission);
    const element = this.elementRef.nativeElement;

    if (!hasPermission) {
      element.disabled = true;
      element.classList.add('role-disabled');
      element.title = 'You do not have permission to perform this action';
    } else {
      element.disabled = false;
      element.classList.remove('role-disabled');
      element.title = '';
    }
  }

  private evaluatePermission(permission: string): boolean {
    if (!permission) return true;

    if (permission.includes(':')) {
      const [module, action] = permission.split(':');
      return this.roleAccessService.hasPermission(module, action as any);
    } else if (permission.startsWith('role:')) {
      const roleName = permission.replace('role:', '');
      return this.roleAccessService.getCurrentUserRole() === roleName;
    } else if (permission.startsWith('level:')) {
      const level = parseInt(permission.replace('level:', ''));
      return this.roleAccessService.hasRoleLevel(level);
    } else {
      return this.roleAccessService.canRead(permission);
    }
  }
}
