import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { SearchComponent } from './search.component';
import { ProductService } from '../../../../core/services/product.service';
import { NoDataConfigService } from '../../../../core/services/no-data-config.service';
import { TestUtilsService } from '../../../../testing/test-utils.service';

describe('SearchComponent', () => {
  let component: SearchComponent;
  let fixture: ComponentFixture<SearchComponent>;
  let testUtils: TestUtilsService;
  let mockProductService: jasmine.SpyObj<ProductService>;
  let mockNoDataConfigService: jasmine.SpyObj<NoDataConfigService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    testUtils = new TestUtilsService();
    
    mockProductService = jasmine.createSpyObj('ProductService', [
      'searchProducts', 'getCategories', 'getProduct'
    ]);
    mockNoDataConfigService = jasmine.createSpyObj('NoDataConfigService', [
      'getSearchConfig'
    ]);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    
    mockActivatedRoute = {
      queryParams: of({}),
      params: of({})
    };

    // Setup default service responses
    mockProductService.searchProducts.and.returnValue(of({
      success: true,
      data: [
        testUtils.generateMockProduct({ name: 'Test Product 1' }),
        testUtils.generateMockProduct({ name: 'Test Product 2' })
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 2,
        totalPages: 1
      }
    }));

    mockProductService.getCategories.and.returnValue(of({
      success: true,
      data: [
        { id: '1', name: 'Electronics', slug: 'electronics' },
        { id: '2', name: 'Clothing', slug: 'clothing' }
      ]
    }));

    mockNoDataConfigService.getSearchConfig.and.returnValue({
      title: 'No Products Found',
      message: 'Try searching with different keywords or browse our categories.',
      iconClass: 'fas fa-search',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Browse Categories',
      secondaryAction: 'Clear Filters',
      suggestions: ['kurtas', 'jeans', 'dresses', 'shoes', 'bags', 'watches'],
      suggestionsTitle: 'Popular searches:'
    });

    await TestBed.configureTestingModule({
      imports: [SearchComponent, HttpClientTestingModule],
      providers: [
        { provide: ProductService, useValue: mockProductService },
        { provide: NoDataConfigService, useValue: mockNoDataConfigService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SearchComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.searchQuery).toBe('');
      expect(component.searchResults).toEqual([]);
      expect(component.loading).toBe(false);
      expect(component.showCategoryModal).toBe(false);
    });

    it('should load no-data config on init', () => {
      fixture.detectChanges();
      
      expect(mockNoDataConfigService.getSearchConfig).toHaveBeenCalled();
      expect(component.noDataConfig).toBeDefined();
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should perform search with query', fakeAsync(() => {
      component.searchQuery = 'test product';
      component.performSearch();
      
      tick(300); // Wait for debounce
      
      expect(mockProductService.searchProducts).toHaveBeenCalledWith({
        query: 'test product',
        category: '',
        filters: {},
        page: 1,
        limit: 20
      });
    }));

    it('should update search results after successful search', fakeAsync(() => {
      component.searchQuery = 'test';
      component.performSearch();
      
      tick(300);
      
      expect(component.searchResults.length).toBe(2);
      expect(component.searchResults[0].name).toBe('Test Product 1');
      expect(component.loading).toBe(false);
    }));

    it('should handle search errors gracefully', fakeAsync(() => {
      mockProductService.searchProducts.and.returnValue(
        throwError(() => testUtils.createHttpError(500, 'Search failed'))
      );
      
      component.searchQuery = 'test';
      component.performSearch();
      
      tick(300);
      
      expect(component.searchResults).toEqual([]);
      expect(component.loading).toBe(false);
    }));

    it('should clear search results', () => {
      component.searchResults = [testUtils.generateMockProduct()];
      component.searchQuery = 'test';
      
      component.clearSearch();
      
      expect(component.searchQuery).toBe('');
      expect(component.searchResults).toEqual([]);
    });

    it('should debounce search input', fakeAsync(() => {
      component.searchQuery = 't';
      component.onSearchInput();
      
      component.searchQuery = 'te';
      component.onSearchInput();
      
      component.searchQuery = 'test';
      component.onSearchInput();
      
      tick(250); // Less than debounce time
      expect(mockProductService.searchProducts).not.toHaveBeenCalled();
      
      tick(100); // Complete debounce time
      expect(mockProductService.searchProducts).toHaveBeenCalledTimes(1);
    }));
  });

  describe('Category Selection', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should open category selection modal', () => {
      component.openCategorySelection();
      
      expect(component.showCategoryModal).toBe(true);
    });

    it('should close category selection modal', () => {
      component.showCategoryModal = true;
      
      component.closeCategorySelection();
      
      expect(component.showCategoryModal).toBe(false);
    });

    it('should select category and perform search', fakeAsync(() => {
      const category = { id: '1', name: 'Electronics', slug: 'electronics' };
      
      component.selectCategory(category);
      
      expect(component.selectedCategory).toBe('electronics');
      expect(component.showCategoryModal).toBe(false);
      
      tick(300);
      expect(mockProductService.searchProducts).toHaveBeenCalledWith(
        jasmine.objectContaining({ category: 'electronics' })
      );
    }));
  });

  describe('Filters', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should apply price filter', fakeAsync(() => {
      component.applyPriceFilter(100, 500);
      
      tick(300);
      
      expect(mockProductService.searchProducts).toHaveBeenCalledWith(
        jasmine.objectContaining({
          filters: jasmine.objectContaining({
            minPrice: 100,
            maxPrice: 500
          })
        })
      );
    }));

    it('should apply brand filter', fakeAsync(() => {
      component.applyBrandFilter(['Nike', 'Adidas']);
      
      tick(300);
      
      expect(mockProductService.searchProducts).toHaveBeenCalledWith(
        jasmine.objectContaining({
          filters: jasmine.objectContaining({
            brands: ['Nike', 'Adidas']
          })
        })
      );
    }));

    it('should clear all filters', fakeAsync(() => {
      component.filters = {
        minPrice: 100,
        maxPrice: 500,
        brands: ['Nike'],
        rating: 4
      };
      
      component.clearFilters();
      
      expect(component.filters).toEqual({});
      
      tick(300);
      expect(mockProductService.searchProducts).toHaveBeenCalledWith(
        jasmine.objectContaining({ filters: {} })
      );
    }));
  });

  describe('Sorting', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should sort by price low to high', fakeAsync(() => {
      component.sortBy('price_asc');
      
      tick(300);
      
      expect(mockProductService.searchProducts).toHaveBeenCalledWith(
        jasmine.objectContaining({ sortBy: 'price_asc' })
      );
    }));

    it('should sort by rating', fakeAsync(() => {
      component.sortBy('rating');
      
      tick(300);
      
      expect(mockProductService.searchProducts).toHaveBeenCalledWith(
        jasmine.objectContaining({ sortBy: 'rating' })
      );
    }));
  });

  describe('Product Interactions', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should navigate to product details', () => {
      const mockProduct = testUtils.generateMockProduct();
      
      component.viewProduct(mockProduct);
      
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/product', mockProduct._id]);
    });

    it('should add product to cart', () => {
      const mockProduct = testUtils.generateMockProduct();
      spyOn(component, 'addToCart');
      
      component.addToCart(mockProduct);
      
      expect(component.addToCart).toHaveBeenCalledWith(mockProduct);
    });
  });

  describe('Template Rendering', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should display search input', () => {
      testUtils.expectElementToExist(fixture, '.search-input');
    });

    it('should display category selection button', () => {
      testUtils.expectElementToExist(fixture, '.category-btn');
    });

    it('should display search results when available', () => {
      component.searchResults = [testUtils.generateMockProduct()];
      fixture.detectChanges();
      
      testUtils.expectElementToExist(fixture, '.search-results');
      testUtils.expectElementToExist(fixture, '.product-card');
    });

    it('should display no-data component when no results', () => {
      component.searchResults = [];
      component.loading = false;
      fixture.detectChanges();
      
      testUtils.expectElementToExist(fixture, 'app-no-data');
    });

    it('should display loading spinner when searching', () => {
      component.loading = true;
      fixture.detectChanges();
      
      testUtils.expectElementToExist(fixture, '.loading-spinner');
    });

    it('should display category modal when open', () => {
      component.showCategoryModal = true;
      fixture.detectChanges();
      
      testUtils.expectElementToExist(fixture, '.category-selection-overlay');
    });
  });

  describe('Query Parameters', () => {
    it('should handle search query from URL', () => {
      mockActivatedRoute.queryParams = of({ q: 'test search' });
      
      fixture.detectChanges();
      
      expect(component.searchQuery).toBe('test search');
    });

    it('should handle category from URL', () => {
      mockActivatedRoute.queryParams = of({ category: 'electronics' });
      
      fixture.detectChanges();
      
      expect(component.selectedCategory).toBe('electronics');
    });

    it('should update URL when search changes', () => {
      component.searchQuery = 'new search';
      component.updateURL();
      
      expect(mockRouter.navigate).toHaveBeenCalledWith([], {
        queryParams: { q: 'new search' },
        queryParamsHandling: 'merge'
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should have proper ARIA labels for search input', () => {
      const searchInput = testUtils.getElementBySelector(fixture, '.search-input');
      expect(searchInput?.nativeElement.getAttribute('aria-label')).toBeTruthy();
    });

    it('should have proper keyboard navigation', () => {
      const searchInput = testUtils.getElementBySelector(fixture, '.search-input');
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      
      searchInput?.nativeElement.dispatchEvent(enterEvent);
      
      // Should trigger search
      expect(component.searchQuery).toBeDefined();
    });
  });
});
