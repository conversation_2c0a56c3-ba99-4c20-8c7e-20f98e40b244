# DFashion Project - Git Ignore File
# =====================================

# Node.js Dependencies
# --------------------
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Package Lock Files (choose one based on your package manager)
# package-lock.json  # Uncomment if using yarn
# yarn.lock          # Uncomment if using npm

# Runtime Data
# ------------
pids
*.pid
*.seed
*.pid.lock

# Coverage Directory
# ------------------
lib-cov
coverage/
*.lcov
.nyc_output

# Grunt Intermediate Storage
# --------------------------
.grunt

# Bower Dependency Directory
# ---------------------------
bower_components

# Node-waf Configuration
# ----------------------
.lock-wscript

# Compiled Binary Addons
# -----------------------
build/Release

# Dependency Directories
# ----------------------
jspm_packages/

# TypeScript Cache
# ----------------
*.tsbuildinfo

# Optional npm Cache Directory
# ----------------------------
.npm

# Optional eslint Cache
# ---------------------
.eslintcache

# Optional stylelint Cache
# ------------------------
.stylelintcache

# Microbundle Cache
# -----------------
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL History
# ---------------------
.node_repl_history

# Output of 'npm pack'
# --------------------
*.tgz

# Yarn Integrity File
# -------------------
.yarn-integrity

# Environment Variables
# ---------------------
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.production
.env.staging

# Parcel-bundler Cache
# --------------------
.cache
.parcel-cache

# Next.js Build Output
# --------------------
.next
out/

# Nuxt.js Build / Generate Output
# -------------------------------
.nuxt
dist/

# Gatsby Files
# ------------
.cache/
public

# Vuepress Build Output
# ---------------------
.vuepress/dist

# Serverless Directories
# ----------------------
.serverless/

# FuseBox Cache
# -------------
.fusebox/

# DynamoDB Local Files
# --------------------
.dynamodb/

# TernJS Port File
# ----------------
.tern-port

# Stores VSCode Versions
# ----------------------
.vscode-test

# Yarn v2
# --------
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Angular Specific
# ----------------
# Compiled Output
frontend/dist/
frontend/tmp/
frontend/out-tsc/
frontend/bazel-out

# Node Modules
frontend/node_modules/

# IDEs and Editors
frontend/.idea/
frontend/.project
frontend/.classpath
frontend/.c9/
frontend/*.launch
frontend/.settings/
frontend/*.sublime-workspace

# Visual Studio Code
frontend/.vscode/*
!frontend/.vscode/settings.json
!frontend/.vscode/tasks.json
!frontend/.vscode/launch.json
!frontend/.vscode/extensions.json
frontend/.history/*

# Miscellaneous
frontend/.angular/cache
frontend/.sass-cache/
frontend/connect.lock
frontend/coverage/
frontend/libpeerconnection.log
frontend/testem.log
frontend/typings

# System Files
frontend/.DS_Store
frontend/Thumbs.db

# Backend Specific
# ----------------
# Logs
backend/logs/
backend/*.log
backend/npm-debug.log*
backend/yarn-debug.log*
backend/yarn-error.log*

# Runtime Data
backend/pids
backend/*.pid
backend/*.seed
backend/*.pid.lock

# Coverage Directory
backend/coverage/

# Uploads and User Generated Content
backend/uploads/
backend/public/uploads/
backend/temp/
backend/tmp/

# Database Files
backend/*.db
backend/*.sqlite
backend/*.sqlite3
backend/database.json

# Cache Files
backend/.cache/
backend/.tmp/

# Build Output
backend/build/
backend/dist/

# IDE and Editor Files
# --------------------
.vscode/
.idea/
*.swp
*.swo
*~

# OS Generated Files
# ------------------
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Windows
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# macOS
.AppleDouble
.LSOverride
Icon
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Backup Files
# ------------
*.bak
*.backup
*.old
*.orig
*.tmp

# Log Files
# ---------
*.log
logs/
log/

# Test Coverage
# -------------
coverage/
.nyc_output/
*.lcov

# Temporary Files
# ---------------
temp/
tmp/
.temp/
.tmp/

# Configuration Files (Sensitive)
# --------------------------------
config/production.json
config/staging.json
config/local.json
.config

# SSL Certificates
# ----------------
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# Docker
# ------
.dockerignore
Dockerfile.prod
docker-compose.override.yml

# Kubernetes
# ----------
*.yaml.local
*.yml.local

# Terraform
# ---------
*.tfstate
*.tfstate.*
.terraform/

# AWS
# ---
.aws/

# Google Cloud
# ------------
.gcloud/

# Azure
# -----
.azure/

# Firebase
# --------
.firebase/
firebase-debug.log
firestore-debug.log

# Sentry
# ------
.sentryclirc

# Documentation Build
# -------------------
docs/_build/
docs/build/

# Python (if any Python scripts)
# -------------------------------
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java (if any Java components)
# ------------------------------
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Compiled Source
# ---------------
*.com
*.exe
*.o
*.so

# Archives
# --------
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Test Files (Optional - Remove if needed)
# --------------------------------------------------
# test-data/ # Uncomment to exclude test data

# Custom Project Specific
# ------------------------
# Add any project-specific files or folders here
*.local
.local/
private/
secrets/
credentials/
