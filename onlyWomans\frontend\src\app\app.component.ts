import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'ow-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet],
  template: `
    <div class="app-container" [class.loading]="isLoading">
      <!-- Loading Screen -->
      <div *ngIf="isLoading" class="loading-screen">
        <div class="loading-content">
          <div class="logo-container">
            <h1 class="app-logo">
              <span class="logo-icon">🌸</span>
              <span class="logo-text">OnlyWomans</span>
            </h1>
            <p class="tagline">Fashion for Every Woman</p>
          </div>
          
          <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
          
          <p class="loading-text">{{ loadingText }}</p>
        </div>
      </div>

      <!-- Main App Content -->
      <div *ngIf="!isLoading" class="main-content fade-in-up">
        <router-outlet></router-outlet>
      </div>

      <!-- Background Decorations -->
      <div class="bg-decorations">
        <div class="decoration decoration-1"></div>
        <div class="decoration decoration-2"></div>
        <div class="decoration decoration-3"></div>
      </div>
    </div>
  `,
  styles: [`
    .app-container {
      min-height: 100vh;
      position: relative;
      overflow-x: hidden;
      background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
    }

    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--primary-100), var(--secondary-100));
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }

    .loading-content {
      text-align: center;
      animation: fadeInUp 0.8s ease-out;
    }

    .logo-container {
      margin-bottom: var(--space-2xl);
    }

    .app-logo {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-md);
      font-size: var(--font-size-4xl);
      font-weight: var(--font-weight-bold);
      color: var(--primary-600);
      margin: 0 0 var(--space-md) 0;
    }

    .logo-icon {
      font-size: var(--font-size-5xl);
      animation: pulse 2s infinite;
    }

    .logo-text {
      background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .tagline {
      font-size: var(--font-size-lg);
      color: var(--text-secondary);
      font-weight: var(--font-weight-medium);
      margin: 0;
    }

    .loading-spinner {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: var(--space-sm);
      margin-bottom: var(--space-xl);
    }

    .spinner-ring {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: var(--primary-500);
      animation: bounce 1.4s ease-in-out infinite both;
    }

    .spinner-ring:nth-child(1) { animation-delay: -0.32s; }
    .spinner-ring:nth-child(2) { animation-delay: -0.16s; }

    @keyframes bounce {
      0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    .loading-text {
      font-size: var(--font-size-base);
      color: var(--text-secondary);
      margin: 0;
      animation: pulse 2s infinite;
    }

    .main-content {
      position: relative;
      z-index: 1;
    }

    .bg-decorations {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 0;
    }

    .decoration {
      position: absolute;
      border-radius: 50%;
      opacity: 0.1;
      animation: float 6s ease-in-out infinite;
    }

    .decoration-1 {
      width: 200px;
      height: 200px;
      background: var(--primary-300);
      top: 10%;
      right: 10%;
      animation-delay: 0s;
    }

    .decoration-2 {
      width: 150px;
      height: 150px;
      background: var(--secondary-300);
      bottom: 20%;
      left: 5%;
      animation-delay: 2s;
    }

    .decoration-3 {
      width: 100px;
      height: 100px;
      background: var(--accent-300);
      top: 50%;
      left: 50%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    .fade-in-up {
      animation: fadeInUp 0.6s ease-out;
    }

    @media (max-width: 768px) {
      .app-logo {
        font-size: var(--font-size-3xl);
        flex-direction: column;
        gap: var(--space-sm);
      }

      .logo-icon {
        font-size: var(--font-size-4xl);
      }

      .decoration {
        display: none;
      }
    }
  `]
})
export class AppComponent implements OnInit {
  title = 'OnlyWomans - Fashion for Every Woman';
  isLoading = true;
  loadingText = 'Loading your fashion world...';

  private loadingTexts = [
    'Loading your fashion world...',
    'Curating the latest trends...',
    'Preparing your style journey...',
    'Setting up your wardrobe...',
    'Almost ready to shop...'
  ];

  constructor(private router: Router) {}

  ngOnInit() {
    this.startLoadingSequence();
    this.setupRouterEvents();
  }

  private startLoadingSequence() {
    let textIndex = 0;
    
    // Change loading text every 800ms
    const textInterval = setInterval(() => {
      textIndex = (textIndex + 1) % this.loadingTexts.length;
      this.loadingText = this.loadingTexts[textIndex];
    }, 800);

    // Hide loading screen after 3 seconds
    setTimeout(() => {
      clearInterval(textInterval);
      this.isLoading = false;
    }, 3000);
  }

  private setupRouterEvents() {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        // Add page-specific classes or analytics here
        this.updatePageMeta(event.url);
      });
  }

  private updatePageMeta(url: string) {
    // Update page title and meta tags based on route
    const pageTitles: { [key: string]: string } = {
      '/': 'OnlyWomans - Fashion for Every Woman',
      '/shop': 'Shop - OnlyWomans',
      '/categories': 'Categories - OnlyWomans',
      '/beauty': 'Beauty & Makeup - OnlyWomans',
      '/accessories': 'Accessories - OnlyWomans',
      '/ethnic': 'Ethnic Wear - OnlyWomans',
      '/western': 'Western Wear - OnlyWomans',
      '/cart': 'Shopping Cart - OnlyWomans',
      '/wishlist': 'My Wishlist - OnlyWomans',
      '/profile': 'My Profile - OnlyWomans'
    };

    const pageTitle = pageTitles[url] || this.title;
    document.title = pageTitle;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 
        'Discover the latest in women\'s fashion, beauty, and accessories. Shop ethnic wear, western outfits, makeup, jewelry and more at OnlyWomans.'
      );
    }
  }
}
