.home-container {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

// Hero Section
.hero-section {
  padding: var(--space-3xl) 0;
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: center;
}

.hero-text {
  .hero-title {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-lg);
    
    .title-line {
      display: block;
      color: var(--text-primary);
    }
    
    .highlight {
      background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--space-2xl);
  }
  
  .hero-actions {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
  }
}

.hero-image {
  position: relative;
  
  .image-container {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    
    .main-image {
      width: 100%;
      height: 500px;
      object-fit: cover;
    }
  }
  
  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  
  .floating-item {
    position: absolute;
    font-size: var(--font-size-2xl);
    animation: float 3s ease-in-out infinite;
    
    &.item-1 {
      top: 20%;
      right: 10%;
      animation-delay: 0s;
    }
    
    &.item-2 {
      top: 60%;
      right: 20%;
      animation-delay: 1s;
    }
    
    &.item-3 {
      bottom: 30%;
      left: 10%;
      animation-delay: 2s;
    }
    
    &.item-4 {
      top: 40%;
      left: 5%;
      animation-delay: 1.5s;
    }
  }
}

// Section Headers
.section-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
  
  .section-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-md);
  }
  
  .section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
  }
}

// Categories Section
.categories-section {
  padding: var(--space-3xl) 0;
  background: var(--bg-primary);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
}

.category-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    
    .category-image img {
      transform: scale(1.1);
    }
    
    .category-overlay {
      opacity: 1;
    }
  }
  
  .category-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform var(--transition-slow);
    }
    
    .category-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(236, 72, 153, 0.8), rgba(168, 85, 247, 0.8));
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity var(--transition-normal);
      
      .category-icon {
        font-size: var(--font-size-4xl);
      }
    }
  }
  
  .category-info {
    padding: var(--space-lg);
    
    .category-name {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin-bottom: var(--space-sm);
    }
    
    .category-description {
      color: var(--text-secondary);
      margin-bottom: var(--space-md);
      line-height: var(--line-height-relaxed);
    }
    
    .category-count {
      color: var(--primary-600);
      font-weight: var(--font-weight-medium);
      font-size: var(--font-size-sm);
    }
  }
}

// Featured Products Section
.featured-section {
  padding: var(--space-3xl) 0;
  background: var(--bg-secondary);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.section-footer {
  text-align: center;
}

// Offers Section
.offers-section {
  padding: var(--space-3xl) 0;
  background: var(--bg-primary);
}

.offers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
}

.offer-card {
  padding: var(--space-2xl);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: transform var(--transition-normal);
  
  &:hover {
    transform: translateY(-4px);
  }
  
  &.offer-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--text-inverse);
  }
  
  &.offer-secondary {
    background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
    color: var(--text-inverse);
  }
  
  &.offer-accent {
    background: linear-gradient(135deg, var(--accent-400), var(--accent-500));
    color: var(--text-inverse);
  }
  
  .offer-content {
    flex: 1;
    
    .offer-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      margin-bottom: var(--space-sm);
    }
    
    .offer-description {
      margin-bottom: var(--space-lg);
      opacity: 0.9;
    }
  }
  
  .offer-icon {
    font-size: var(--font-size-4xl);
    margin-left: var(--space-lg);
  }
}

// Newsletter Section
.newsletter-section {
  padding: var(--space-3xl) 0;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  color: var(--text-inverse);
}

.newsletter-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: center;
}

.newsletter-text {
  .newsletter-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-md);
  }
  
  .newsletter-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    line-height: var(--line-height-relaxed);
  }
}

.newsletter-form {
  .form-group {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
    
    .email-input {
      flex: 1;
      padding: var(--space-md);
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      
      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
      }
    }
  }
  
  .newsletter-note {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    text-align: center;
  }
  
  .hero-text .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .offers-grid {
    grid-template-columns: 1fr;
  }
  
  .offer-card {
    flex-direction: column;
    text-align: center;
    
    .offer-icon {
      margin: var(--space-lg) 0 0 0;
    }
  }
  
  .newsletter-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .newsletter-form .form-group {
    flex-direction: column;
  }
}
