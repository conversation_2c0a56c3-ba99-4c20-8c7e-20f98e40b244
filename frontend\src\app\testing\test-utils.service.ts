import { Injectable } from '@angular/core';
import { ComponentFixture } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';
import { Observable, of, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TestUtilsService {

  // Mock data generators
  generateMockUser(overrides: Partial<any> = {}): any {
    return {
      _id: 'user123',
      username: 'testuser',
      email: '<EMAIL>',
      fullName: 'Test User',
      role: 'buyer',
      avatar: 'https://via.placeholder.com/150',
      isActive: true,
      createdAt: new Date(),
      ...overrides
    };
  }

  generateMockProduct(overrides: Partial<any> = {}): any {
    return {
      _id: 'product123',
      name: 'Test Product',
      description: 'A test product description',
      price: 99.99,
      originalPrice: 129.99,
      brand: 'Test Brand',
      category: 'Test Category',
      images: [
        { url: 'https://via.placeholder.com/300', alt: 'Test Product' }
      ],
      isActive: true,
      stock: 10,
      rating: 4.5,
      reviewCount: 25,
      isNew: false,
      isTrending: true,
      createdAt: new Date(),
      ...overrides
    };
  }

  generateMockCartItem(overrides: Partial<any> = {}): any {
    return {
      _id: 'cartitem123',
      product: this.generateMockProduct(),
      quantity: 1,
      size: 'M',
      color: 'Blue',
      addedAt: new Date(),
      ...overrides
    };
  }

  generateMockOrder(overrides: Partial<any> = {}): any {
    return {
      _id: 'order123',
      orderNumber: 'ORD-2024-001',
      user: this.generateMockUser(),
      items: [this.generateMockCartItem()],
      status: 'pending',
      totalAmount: 99.99,
      shippingAddress: {
        street: '123 Test St',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345',
        country: 'Test Country'
      },
      createdAt: new Date(),
      ...overrides
    };
  }

  generateMockPost(overrides: Partial<any> = {}): any {
    return {
      _id: 'post123',
      user: this.generateMockUser(),
      content: 'Test post content',
      images: ['https://via.placeholder.com/400'],
      products: [this.generateMockProduct()],
      likes: [],
      comments: [],
      shares: [],
      isActive: true,
      createdAt: new Date(),
      ...overrides
    };
  }

  generateMockStory(overrides: Partial<any> = {}): any {
    return {
      _id: 'story123',
      user: this.generateMockUser(),
      content: 'Test story content',
      media: {
        type: 'image',
        url: 'https://via.placeholder.com/300'
      },
      products: [this.generateMockProduct()],
      views: [],
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      createdAt: new Date(),
      ...overrides
    };
  }

  // Mock service creators
  createMockAuthService(user: any = null): any {
    return {
      currentUser$: of(user),
      isAuthenticated$: of(!!user),
      login: jasmine.createSpy('login').and.returnValue(of({ success: true, user, token: 'mock-token' })),
      logout: jasmine.createSpy('logout').and.returnValue(of({ success: true })),
      register: jasmine.createSpy('register').and.returnValue(of({ success: true, user, token: 'mock-token' })),
      getCurrentUser: jasmine.createSpy('getCurrentUser').and.returnValue(user),
      isAuthenticated: !!user,
      getToken: jasmine.createSpy('getToken').and.returnValue('mock-token')
    };
  }

  createMockProductService(): any {
    return {
      getProducts: jasmine.createSpy('getProducts').and.returnValue(of({ 
        success: true, 
        data: [this.generateMockProduct()] 
      })),
      getProduct: jasmine.createSpy('getProduct').and.returnValue(of({ 
        success: true, 
        data: this.generateMockProduct() 
      })),
      getFeaturedBrands: jasmine.createSpy('getFeaturedBrands').and.returnValue(of({ 
        success: true, 
        data: [{ id: '1', name: 'Test Brand', logo: 'test-logo.png' }] 
      })),
      getTrendingProducts: jasmine.createSpy('getTrendingProducts').and.returnValue(of({ 
        success: true, 
        data: [this.generateMockProduct({ isTrending: true })] 
      })),
      getNewArrivals: jasmine.createSpy('getNewArrivals').and.returnValue(of({ 
        success: true, 
        data: [this.generateMockProduct({ isNew: true })] 
      })),
      getCategories: jasmine.createSpy('getCategories').and.returnValue(of({ 
        success: true, 
        data: [{ id: '1', name: 'Test Category', slug: 'test-category' }] 
      })),
      searchProducts: jasmine.createSpy('searchProducts').and.returnValue(of({ 
        success: true, 
        data: [this.generateMockProduct()] 
      }))
    };
  }

  createMockCartService(): any {
    return {
      cartItems$: of([this.generateMockCartItem()]),
      cartItemCount$: of(1),
      cartSummary$: of({ subtotal: 99.99, total: 99.99, itemCount: 1 }),
      getCart: jasmine.createSpy('getCart').and.returnValue(of({ 
        success: true, 
        cart: { items: [this.generateMockCartItem()] },
        summary: { subtotal: 99.99, total: 99.99, itemCount: 1 }
      })),
      addToCart: jasmine.createSpy('addToCart').and.returnValue(of({ success: true })),
      updateCartItem: jasmine.createSpy('updateCartItem').and.returnValue(of({ success: true })),
      removeFromCart: jasmine.createSpy('removeFromCart').and.returnValue(of({ success: true })),
      clearCart: jasmine.createSpy('clearCart').and.returnValue(of({ success: true }))
    };
  }

  createMockLoadingService(): any {
    return {
      isLoading$: of(false),
      loadingState$: of({}),
      startLoading: jasmine.createSpy('startLoading'),
      stopLoading: jasmine.createSpy('stopLoading'),
      isLoadingKey: jasmine.createSpy('isLoadingKey').and.returnValue(of(false)),
      isLoadingKeySync: jasmine.createSpy('isLoadingKeySync').and.returnValue(false),
      wrapWithLoading: jasmine.createSpy('wrapWithLoading').and.callFake((source: Observable<any>) => source)
    };
  }

  createMockErrorHandlerService(): any {
    return {
      errorState$: of({ hasError: false, errors: [], lastError: undefined }),
      handleError: jasmine.createSpy('handleError'),
      handleHttpError: jasmine.createSpy('handleHttpError'),
      clearError: jasmine.createSpy('clearError'),
      clearAllErrors: jasmine.createSpy('clearAllErrors'),
      hasErrors: jasmine.createSpy('hasErrors').and.returnValue(false)
    };
  }

  // DOM testing utilities
  getElementByTestId(fixture: ComponentFixture<any>, testId: string): DebugElement | null {
    return fixture.debugElement.query(By.css(`[data-testid="${testId}"]`));
  }

  getAllElementsByTestId(fixture: ComponentFixture<any>, testId: string): DebugElement[] {
    return fixture.debugElement.queryAll(By.css(`[data-testid="${testId}"]`));
  }

  getElementBySelector(fixture: ComponentFixture<any>, selector: string): DebugElement | null {
    return fixture.debugElement.query(By.css(selector));
  }

  getAllElementsBySelector(fixture: ComponentFixture<any>, selector: string): DebugElement[] {
    return fixture.debugElement.queryAll(By.css(selector));
  }

  clickElement(element: DebugElement): void {
    element.nativeElement.click();
  }

  setInputValue(element: DebugElement, value: string): void {
    element.nativeElement.value = value;
    element.nativeElement.dispatchEvent(new Event('input'));
  }

  triggerEvent(element: DebugElement, eventName: string, eventData?: any): void {
    element.triggerEventHandler(eventName, eventData);
  }

  // Async testing utilities
  waitForAsync(fn: () => void): Promise<void> {
    return new Promise(resolve => {
      setTimeout(() => {
        fn();
        resolve();
      }, 0);
    });
  }

  // Error simulation utilities
  createHttpError(status: number, message: string = 'Test error'): any {
    return {
      status,
      statusText: 'Test Error',
      error: { message },
      url: 'test-url'
    };
  }

  createNetworkError(): Observable<never> {
    return throwError(() => this.createHttpError(0, 'Network error'));
  }

  createServerError(): Observable<never> {
    return throwError(() => this.createHttpError(500, 'Internal server error'));
  }

  createValidationError(): Observable<never> {
    return throwError(() => this.createHttpError(422, 'Validation error'));
  }

  createAuthError(): Observable<never> {
    return throwError(() => this.createHttpError(401, 'Unauthorized'));
  }

  // Component testing utilities
  expectElementToExist(fixture: ComponentFixture<any>, selector: string): void {
    const element = this.getElementBySelector(fixture, selector);
    expect(element).toBeTruthy();
  }

  expectElementNotToExist(fixture: ComponentFixture<any>, selector: string): void {
    const element = this.getElementBySelector(fixture, selector);
    expect(element).toBeFalsy();
  }

  expectElementToHaveText(fixture: ComponentFixture<any>, selector: string, expectedText: string): void {
    const element = this.getElementBySelector(fixture, selector);
    expect(element?.nativeElement.textContent.trim()).toBe(expectedText);
  }

  expectElementToContainText(fixture: ComponentFixture<any>, selector: string, expectedText: string): void {
    const element = this.getElementBySelector(fixture, selector);
    expect(element?.nativeElement.textContent).toContain(expectedText);
  }

  expectElementToHaveClass(fixture: ComponentFixture<any>, selector: string, className: string): void {
    const element = this.getElementBySelector(fixture, selector);
    expect(element?.nativeElement.classList).toContain(className);
  }

  // Form testing utilities
  fillForm(fixture: ComponentFixture<any>, formData: { [key: string]: string }): void {
    Object.keys(formData).forEach(key => {
      const input = this.getElementByTestId(fixture, key) || this.getElementBySelector(fixture, `[name="${key}"]`);
      if (input) {
        this.setInputValue(input, formData[key]);
      }
    });
    fixture.detectChanges();
  }

  submitForm(fixture: ComponentFixture<any>, formSelector: string = 'form'): void {
    const form = this.getElementBySelector(fixture, formSelector);
    if (form) {
      form.triggerEventHandler('ngSubmit', null);
      fixture.detectChanges();
    }
  }
}
