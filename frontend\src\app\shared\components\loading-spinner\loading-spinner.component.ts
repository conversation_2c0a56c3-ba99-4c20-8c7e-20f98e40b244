import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../../core/services/loading.service';

@Component({
  selector: 'app-loading-spinner',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- Global Loading Overlay -->
    <div *ngIf="showGlobalLoading && isGlobalLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner" [class]="webSpinnerType">
          <div *ngIf="webSpinnerType === 'dots'" class="dots-spinner">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>

          <div *ngIf="webSpinnerType === 'circle'" class="circle-spinner"></div>

          <div *ngIf="webSpinnerType === 'pulse'" class="pulse-spinner">
            <div class="pulse-dot"></div>
            <div class="pulse-dot"></div>
            <div class="pulse-dot"></div>
          </div>
        </div>

        <div *ngIf="message" class="loading-message">{{ message }}</div>

        <div *ngIf="showProgress && progress !== undefined" class="loading-progress">
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="progress"></div>
          </div>
          <div class="progress-text">{{ progress }}%</div>
        </div>
      </div>
    </div>

    <!-- Inline Loading Spinner -->
    <div *ngIf="showInlineLoading && isInlineLoading"
         class="inline-loading"
         [class.small]="size === 'small'"
         [class.large]="size === 'large'">

      <div class="spinner" [class]="webSpinnerType">
        <div *ngIf="webSpinnerType === 'dots'" class="dots-spinner">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>

        <div *ngIf="webSpinnerType === 'circle'" class="circle-spinner"></div>

        <div *ngIf="webSpinnerType === 'pulse'" class="pulse-spinner">
          <div class="pulse-dot"></div>
          <div class="pulse-dot"></div>
          <div class="pulse-dot"></div>
        </div>
      </div>

      <div *ngIf="message" class="loading-message">{{ message }}</div>
    </div>

    <!-- Legacy Ionic Spinner (for backward compatibility) -->
    <div *ngIf="overlay && !showGlobalLoading" class="loading-container overlay">
      <div class="spinner-wrapper">
        <div class="circle-spinner"></div>
        <p *ngIf="message" class="loading-message">{{ message }}</p>
      </div>
    </div>
  `,
  styleUrls: ['./loading-spinner.component.scss']
})
export class LoadingSpinnerComponent implements OnInit, OnDestroy {
  @Input() message: string = '';
  @Input() spinnerType: string = 'crescent'; // Legacy Ionic spinner type
  @Input() webSpinnerType: 'dots' | 'circle' | 'pulse' = 'circle'; // New web spinner types
  @Input() color: string = 'primary';
  @Input() overlay: boolean = false;
  @Input() showGlobalLoading: boolean = false;
  @Input() showInlineLoading: boolean = false;
  @Input() loadingKey?: string;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() showProgress: boolean = false;
  @Input() progress?: number;

  isGlobalLoading: boolean = false;
  isInlineLoading: boolean = false;

  private subscription: Subscription = new Subscription();

  constructor(private loadingService: LoadingService) {}

  ngOnInit() {
    if (this.showGlobalLoading) {
      this.subscription.add(
        this.loadingService.isLoading$.subscribe(loading => {
          this.isGlobalLoading = loading;
        })
      );
    }

    if (this.loadingKey) {
      this.subscription.add(
        this.loadingService.isLoadingKey(this.loadingKey).subscribe(loading => {
          this.isInlineLoading = loading;
        })
      );

      // Subscribe to loading state for progress and message updates
      this.subscription.add(
        this.loadingService.loadingState$.subscribe(state => {
          const loadingState = state[this.loadingKey!];
          if (loadingState) {
            this.message = this.message || loadingState.message;
            this.progress = loadingState.progress;
          }
        })
      );
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
