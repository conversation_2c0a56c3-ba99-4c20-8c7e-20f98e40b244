<div class="dashboard-container">
  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading dashboard data...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading" class="dashboard-content">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-text">
        <h1>Welcome back, {{ (currentUser$ | async)?.fullName }}!</h1>
        <p>Here's what's happening with your business today.</p>
      </div>
      <div class="welcome-actions">
        <button mat-raised-button color="primary" (click)="refreshData()">
          <mat-icon>refresh</mat-icon>
          Refresh Data
        </button>
      </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="stats-grid">
      <mat-card *ngFor="let stat of quickStats" class="stat-card" [style.border-left-color]="stat.color">
        <mat-card-content>
          <div class="stat-header">
            <div class="stat-icon" [style.background-color]="stat.color">
              <mat-icon>{{ stat.icon }}</mat-icon>
            </div>
            <div class="stat-change" [ngClass]="getChangeClass(stat.changeType)">
              {{ stat.change }}
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              <span *ngIf="stat.prefix">{{ stat.prefix }}</span>
              {{ stat.title === 'Revenue' ? formatCurrency(stat.value) : formatNumber(stat.value) }}
            </div>
            <div class="stat-title">{{ stat.title }}</div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- User Growth Chart -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>User Growth</mat-card-title>
            <mat-card-subtitle>Monthly new user registrations</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container">
              <div class="chart-placeholder">
                <mat-icon>show_chart</mat-icon>
                <h3>User Growth Chart</h3>
                <p>Chart visualization will be displayed here</p>
                <div class="mock-data">
                  <small>Sample data: {{ userGrowthChartData.data.join(', ') }}</small>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Order Trends Chart -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>Order Trends</mat-card-title>
            <mat-card-subtitle>Daily orders this week</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container">
              <div class="chart-placeholder">
                <mat-icon>bar_chart</mat-icon>
                <h3>Order Trends Chart</h3>
                <p>Chart visualization will be displayed here</p>
                <div class="mock-data">
                  <small>Sample data: {{ orderTrendsChartData.data.join(', ') }}</small>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Revenue Distribution Chart -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>Revenue Distribution</mat-card-title>
            <mat-card-subtitle>Revenue by category</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container">
              <div class="chart-placeholder">
                <mat-icon>donut_large</mat-icon>
                <h3>Revenue Distribution Chart</h3>
                <p>Chart visualization will be displayed here</p>
                <div class="mock-data">
                  <small>Sample data: {{ revenueChartData.data.join(', ') }}</small>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Bottom Section -->
    <div class="bottom-section">
      <!-- Recent Activities -->
      <mat-card class="activities-card">
        <mat-card-header>
          <mat-card-title>Recent Activities</mat-card-title>
          <mat-card-subtitle>Latest system activities</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="activities-list">
            <div *ngFor="let activity of recentActivities" class="activity-item">
              <div class="activity-icon" [style.background-color]="activity.color">
                <mat-icon>{{ activity.icon }}</mat-icon>
              </div>
              <div class="activity-content">
                <div class="activity-message">{{ activity.message }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
          <div class="activities-footer">
            <button mat-button color="primary">View All Activities</button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Quick Actions -->
      <mat-card class="actions-card">
        <mat-card-header>
          <mat-card-title>Quick Actions</mat-card-title>
          <mat-card-subtitle>Common administrative tasks</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="actions-grid">
            <button mat-raised-button class="action-button" (click)="navigateToSection('users')">
              <mat-icon>person_add</mat-icon>
              <span>Add User</span>
            </button>
            <button mat-raised-button class="action-button" (click)="navigateToSection('products')">
              <mat-icon>add_box</mat-icon>
              <span>Add Product</span>
            </button>
            <button mat-raised-button class="action-button" (click)="navigateToSection('orders')">
              <mat-icon>list_alt</mat-icon>
              <span>View Orders</span>
            </button>
            <button mat-raised-button class="action-button">
              <mat-icon>analytics</mat-icon>
              <span>View Reports</span>
            </button>
            <button mat-raised-button class="action-button">
              <mat-icon>settings</mat-icon>
              <span>Settings</span>
            </button>
            <button mat-raised-button class="action-button">
              <mat-icon>help</mat-icon>
              <span>Help Center</span>
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- System Status -->
    <mat-card class="status-card">
      <mat-card-header>
        <mat-card-title>System Status</mat-card-title>
        <mat-card-subtitle>Current system health and performance</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-indicator online"></div>
            <div class="status-info">
              <div class="status-label">API Server</div>
              <div class="status-value">Online</div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-indicator online"></div>
            <div class="status-info">
              <div class="status-label">Database</div>
              <div class="status-value">Connected</div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-indicator warning"></div>
            <div class="status-info">
              <div class="status-label">Storage</div>
              <div class="status-value">85% Used</div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-indicator online"></div>
            <div class="status-info">
              <div class="status-label">Payment Gateway</div>
              <div class="status-value">Active</div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
