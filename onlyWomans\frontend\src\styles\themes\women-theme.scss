// OnlyWomans - Women's Fashion Theme
// Elegant, feminine, and modern design system

// 🌸 Color Palette - Feminine & Elegant
:root {
  // Primary Colors - Rose & Pink Tones
  --primary-50: #fdf2f8;
  --primary-100: #fce7f3;
  --primary-200: #fbcfe8;
  --primary-300: #f9a8d4;
  --primary-400: #f472b6;
  --primary-500: #ec4899;  // Main brand color
  --primary-600: #db2777;
  --primary-700: #be185d;
  --primary-800: #9d174d;
  --primary-900: #831843;

  // Secondary Colors - Soft Purple & Lavender
  --secondary-50: #faf5ff;
  --secondary-100: #f3e8ff;
  --secondary-200: #e9d5ff;
  --secondary-300: #d8b4fe;
  --secondary-400: #c084fc;
  --secondary-500: #a855f7;
  --secondary-600: #9333ea;
  --secondary-700: #7c3aed;
  --secondary-800: #6b21a8;
  --secondary-900: #581c87;

  // Accent Colors - Gold & Rose Gold
  --accent-50: #fffbeb;
  --accent-100: #fef3c7;
  --accent-200: #fde68a;
  --accent-300: #fcd34d;
  --accent-400: #fbbf24;
  --accent-500: #f59e0b;
  --accent-600: #d97706;
  --accent-700: #b45309;
  --accent-800: #92400e;
  --accent-900: #78350f;

  // Neutral Colors - Warm Grays
  --neutral-50: #fafaf9;
  --neutral-100: #f5f5f4;
  --neutral-200: #e7e5e4;
  --neutral-300: #d6d3d1;
  --neutral-400: #a8a29e;
  --neutral-500: #78716c;
  --neutral-600: #57534e;
  --neutral-700: #44403c;
  --neutral-800: #292524;
  --neutral-900: #1c1917;

  // Semantic Colors
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  // Background Colors
  --bg-primary: #ffffff;
  --bg-secondary: var(--neutral-50);
  --bg-tertiary: var(--primary-50);
  --bg-accent: linear-gradient(135deg, var(--primary-100), var(--secondary-100));

  // Text Colors
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-400);
  --text-inverse: #ffffff;

  // Border Colors
  --border-light: var(--neutral-200);
  --border-medium: var(--neutral-300);
  --border-dark: var(--neutral-400);

  // Shadow Colors
  --shadow-sm: 0 1px 2px 0 rgba(236, 72, 153, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(236, 72, 153, 0.1), 0 4px 6px -2px rgba(236, 72, 153, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(236, 72, 153, 0.1), 0 10px 10px -5px rgba(236, 72, 153, 0.04);

  // Spacing Scale
  --space-xs: 0.25rem;   // 4px
  --space-sm: 0.5rem;    // 8px
  --space-md: 1rem;      // 16px
  --space-lg: 1.5rem;    // 24px
  --space-xl: 2rem;      // 32px
  --space-2xl: 3rem;     // 48px
  --space-3xl: 4rem;     // 64px

  // Border Radius
  --radius-sm: 0.375rem;  // 6px
  --radius-md: 0.5rem;    // 8px
  --radius-lg: 0.75rem;   // 12px
  --radius-xl: 1rem;      // 16px
  --radius-2xl: 1.5rem;   // 24px
  --radius-full: 9999px;

  // Typography Scale
  --font-size-xs: 0.75rem;    // 12px
  --font-size-sm: 0.875rem;   // 14px
  --font-size-base: 1rem;     // 16px
  --font-size-lg: 1.125rem;   // 18px
  --font-size-xl: 1.25rem;    // 20px
  --font-size-2xl: 1.5rem;    // 24px
  --font-size-3xl: 1.875rem;  // 30px
  --font-size-4xl: 2.25rem;   // 36px
  --font-size-5xl: 3rem;      // 48px

  // Font Weights
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  // Line Heights
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  // Transitions
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  // Z-Index Scale
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// 🌸 Component Styles

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  // Primary Button - Rose Pink
  &.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
      box-shadow: var(--shadow-lg);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // Secondary Button - Soft Purple
  &.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
      box-shadow: var(--shadow-lg);
      transform: translateY(-1px);
    }
  }

  // Outline Button
  &.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-500);

    &:hover:not(:disabled) {
      background: var(--primary-500);
      color: var(--text-inverse);
    }
  }

  // Ghost Button
  &.btn-ghost {
    background: transparent;
    color: var(--primary-600);

    &:hover:not(:disabled) {
      background: var(--primary-50);
    }
  }

  // Size Variants
  &.btn-sm {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-sm);
  }

  &.btn-lg {
    padding: var(--space-md) var(--space-2xl);
    font-size: var(--font-size-lg);
  }

  // Icon Button
  &.btn-icon {
    padding: var(--space-sm);
    border-radius: var(--radius-full);
    aspect-ratio: 1;
  }
}

// Cards
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);

  &:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }

  .card-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
  }

  .card-body {
    padding: var(--space-lg);
  }

  .card-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-light);
    background: var(--bg-secondary);
  }
}

// Product Cards
.product-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;

  &:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
  }

  .product-image {
    position: relative;
    aspect-ratio: 3/4;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform var(--transition-slow);
    }

    &:hover img {
      transform: scale(1.05);
    }

    .product-badge {
      position: absolute;
      top: var(--space-sm);
      left: var(--space-sm);
      background: var(--primary-500);
      color: var(--text-inverse);
      padding: var(--space-xs) var(--space-sm);
      border-radius: var(--radius-full);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-semibold);
    }

    .wishlist-btn {
      position: absolute;
      top: var(--space-sm);
      right: var(--space-sm);
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: var(--radius-full);
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all var(--transition-normal);

      &:hover {
        background: var(--primary-500);
        color: var(--text-inverse);
      }

      &.active {
        background: var(--primary-500);
        color: var(--text-inverse);
      }
    }
  }

  .product-info {
    padding: var(--space-lg);

    .product-brand {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin-bottom: var(--space-xs);
    }

    .product-name {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin-bottom: var(--space-sm);
      line-height: var(--line-height-tight);
    }

    .product-price {
      display: flex;
      align-items: center;
      gap: var(--space-sm);
      margin-bottom: var(--space-sm);

      .current-price {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--primary-600);
      }

      .original-price {
        font-size: var(--font-size-base);
        color: var(--text-tertiary);
        text-decoration: line-through;
      }

      .discount {
        background: var(--accent-500);
        color: var(--text-inverse);
        padding: var(--space-xs) var(--space-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
      }
    }

    .product-rating {
      display: flex;
      align-items: center;
      gap: var(--space-xs);
      margin-bottom: var(--space-md);

      .stars {
        color: var(--accent-500);
      }

      .rating-text {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
      }
    }
  }
}

// Form Elements
.form-group {
  margin-bottom: var(--space-lg);

  label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
  }

  input, textarea, select {
    width: 100%;
    padding: var(--space-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);

    &:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.1);
    }

    &::placeholder {
      color: var(--text-tertiary);
    }
  }

  .form-error {
    color: var(--error);
    font-size: var(--font-size-sm);
    margin-top: var(--space-xs);
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// Utility Classes
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

// Responsive Design
@media (max-width: 768px) {
  :root {
    --space-lg: 1rem;
    --space-xl: 1.5rem;
    --space-2xl: 2rem;
  }

  .btn {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
  }

  .product-card .product-info {
    padding: var(--space-md);
  }
}
