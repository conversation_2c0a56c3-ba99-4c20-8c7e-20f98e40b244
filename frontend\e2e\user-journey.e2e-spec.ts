import { browser, by, element, ElementFinder, ElementArrayFinder } from 'protractor';

describe('Fashion E-commerce User Journey', () => {
  
  beforeEach(async () => {
    await browser.get('/');
  });

  describe('Guest User Journey', () => {
    it('should allow guest to browse products', async () => {
      // Navigate to shop page
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Wait for products to load
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      // Verify products are displayed
      const productCards = element.all(by.css('.product-card'));
      expect(await productCards.count()).toBeGreaterThan(0);
      
      // Verify featured brands section
      const brandsSection = element(by.css('.featured-brands-section'));
      expect(await brandsSection.isDisplayed()).toBe(true);
      
      // Verify trending products section
      const trendingSection = element(by.css('.trending-section'));
      expect(await trendingSection.isDisplayed()).toBe(true);
    });

    it('should allow guest to search for products', async () => {
      // Navigate to search page
      await element(by.css('[data-testid="search-link"]')).click();
      
      // Enter search query
      const searchInput = element(by.css('.search-input'));
      await searchInput.sendKeys('dress');
      
      // Wait for search results
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      // Verify search results
      const searchResults = element.all(by.css('.product-card'));
      expect(await searchResults.count()).toBeGreaterThan(0);
      
      // Verify search query is displayed
      expect(await searchInput.getAttribute('value')).toBe('dress');
    });

    it('should show category selection modal', async () => {
      await element(by.css('[data-testid="search-link"]')).click();
      
      // Click category selection button
      await element(by.css('.category-btn')).click();
      
      // Verify modal is displayed
      const modal = element(by.css('.category-selection-overlay'));
      expect(await modal.isDisplayed()).toBe(true);
      
      // Select a category
      await element(by.css('.category-option')).click();
      
      // Verify modal is closed
      expect(await modal.isPresent()).toBe(false);
    });

    it('should display product details', async () => {
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Wait for products and click first one
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      await element.all(by.css('.product-card')).first().click();
      
      // Verify product details page
      expect(await browser.getCurrentUrl()).toContain('/product/');
      
      // Verify product information is displayed
      const productName = element(by.css('.product-name'));
      const productPrice = element(by.css('.product-price'));
      const productImage = element(by.css('.product-image'));
      
      expect(await productName.isDisplayed()).toBe(true);
      expect(await productPrice.isDisplayed()).toBe(true);
      expect(await productImage.isDisplayed()).toBe(true);
    });
  });

  describe('User Authentication Journey', () => {
    it('should allow user to register', async () => {
      // Navigate to register page
      await element(by.css('[data-testid="register-link"]')).click();
      
      // Fill registration form
      await element(by.css('[data-testid="fullName"]')).sendKeys('Test User');
      await element(by.css('[data-testid="username"]')).sendKeys('testuser123');
      await element(by.css('[data-testid="email"]')).sendKeys('<EMAIL>');
      await element(by.css('[data-testid="password"]')).sendKeys('password123');
      await element(by.css('[data-testid="confirmPassword"]')).sendKeys('password123');
      
      // Submit form
      await element(by.css('[data-testid="register-btn"]')).click();
      
      // Wait for redirect to dashboard or home
      await browser.wait(() => {
        return browser.getCurrentUrl().then(url => !url.includes('/register'));
      }, 10000);
      
      // Verify successful registration (should be redirected)
      expect(await browser.getCurrentUrl()).not.toContain('/register');
    });

    it('should allow user to login', async () => {
      // Navigate to login page
      await element(by.css('[data-testid="login-link"]')).click();
      
      // Fill login form
      await element(by.css('[data-testid="email"]')).sendKeys('<EMAIL>');
      await element(by.css('[data-testid="password"]')).sendKeys('password123');
      
      // Submit form
      await element(by.css('[data-testid="login-btn"]')).click();
      
      // Wait for redirect
      await browser.wait(() => {
        return browser.getCurrentUrl().then(url => !url.includes('/login'));
      }, 10000);
      
      // Verify successful login
      const userMenu = element(by.css('[data-testid="user-menu"]'));
      expect(await userMenu.isDisplayed()).toBe(true);
    });
  });

  describe('Authenticated User Journey', () => {
    beforeEach(async () => {
      // Login before each test
      await element(by.css('[data-testid="login-link"]')).click();
      await element(by.css('[data-testid="email"]')).sendKeys('<EMAIL>');
      await element(by.css('[data-testid="password"]')).sendKeys('password123');
      await element(by.css('[data-testid="login-btn"]')).click();
      
      await browser.wait(() => {
        return browser.getCurrentUrl().then(url => !url.includes('/login'));
      }, 10000);
    });

    it('should allow user to add products to cart', async () => {
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Wait for products
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      // Add first product to cart
      await element.all(by.css('.add-to-cart-btn')).first().click();
      
      // Verify cart count updated
      const cartCount = element(by.css('[data-testid="cart-count"]'));
      expect(await cartCount.getText()).toBe('1');
    });

    it('should allow user to view cart', async () => {
      // Add product to cart first
      await element(by.css('[data-testid="shop-link"]')).click();
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      await element.all(by.css('.add-to-cart-btn')).first().click();
      
      // Navigate to cart
      await element(by.css('[data-testid="cart-link"]')).click();
      
      // Verify cart items are displayed
      const cartItems = element.all(by.css('.cart-item'));
      expect(await cartItems.count()).toBeGreaterThan(0);
      
      // Verify cart summary
      const cartSummary = element(by.css('.cart-summary'));
      expect(await cartSummary.isDisplayed()).toBe(true);
    });

    it('should allow user to add products to wishlist', async () => {
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Wait for products
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      // Add first product to wishlist
      await element.all(by.css('.wishlist-btn')).first().click();
      
      // Navigate to wishlist
      await element(by.css('[data-testid="wishlist-link"]')).click();
      
      // Verify wishlist items
      const wishlistItems = element.all(by.css('.wishlist-item'));
      expect(await wishlistItems.count()).toBeGreaterThan(0);
    });

    it('should allow user to view profile', async () => {
      // Navigate to profile
      await element(by.css('[data-testid="profile-link"]')).click();
      
      // Verify profile sections
      const profileHeader = element(by.css('.profile-header'));
      const profileSections = element.all(by.css('.section-card'));
      
      expect(await profileHeader.isDisplayed()).toBe(true);
      expect(await profileSections.count()).toBeGreaterThan(0);
      
      // Verify user information
      const userName = element(by.css('.user-name'));
      const userEmail = element(by.css('.user-email'));
      
      expect(await userName.isDisplayed()).toBe(true);
      expect(await userEmail.isDisplayed()).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should display error message for invalid login', async () => {
      await element(by.css('[data-testid="login-link"]')).click();
      
      // Enter invalid credentials
      await element(by.css('[data-testid="email"]')).sendKeys('<EMAIL>');
      await element(by.css('[data-testid="password"]')).sendKeys('wrongpassword');
      
      // Submit form
      await element(by.css('[data-testid="login-btn"]')).click();
      
      // Verify error message
      const errorMessage = element(by.css('.error-message'));
      expect(await errorMessage.isDisplayed()).toBe(true);
    });

    it('should display no-data state when no products found', async () => {
      await element(by.css('[data-testid="search-link"]')).click();
      
      // Search for non-existent product
      const searchInput = element(by.css('.search-input'));
      await searchInput.sendKeys('nonexistentproduct12345');
      
      // Wait for search to complete
      await browser.sleep(2000);
      
      // Verify no-data component
      const noDataComponent = element(by.css('app-no-data'));
      expect(await noDataComponent.isDisplayed()).toBe(true);
    });
  });

  describe('Responsive Design', () => {
    it('should work on mobile viewport', async () => {
      // Set mobile viewport
      await browser.manage().window().setSize(375, 667);
      
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Verify mobile navigation
      const mobileMenu = element(by.css('.mobile-menu'));
      expect(await mobileMenu.isDisplayed()).toBe(true);
      
      // Verify products are still displayed
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      const productCards = element.all(by.css('.product-card'));
      expect(await productCards.count()).toBeGreaterThan(0);
    });

    it('should work on tablet viewport', async () => {
      // Set tablet viewport
      await browser.manage().window().setSize(768, 1024);
      
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Verify layout adapts to tablet
      const container = element(by.css('.shop-container'));
      expect(await container.isDisplayed()).toBe(true);
      
      // Verify products grid layout
      const productsGrid = element(by.css('.products-grid'));
      expect(await productsGrid.isDisplayed()).toBe(true);
    });
  });

  describe('Performance', () => {
    it('should load shop page within acceptable time', async () => {
      const startTime = Date.now();
      
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Wait for products to load
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      const loadTime = Date.now() - startTime;
      
      // Should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
    });

    it('should handle large product lists efficiently', async () => {
      await element(by.css('[data-testid="shop-link"]')).click();
      
      // Wait for initial load
      await browser.wait(() => {
        return element.all(by.css('.product-card')).count().then(count => count > 0);
      }, 10000);
      
      // Scroll to trigger lazy loading
      await browser.executeScript('window.scrollTo(0, document.body.scrollHeight)');
      
      // Verify more products load
      await browser.sleep(2000);
      const productCards = element.all(by.css('.product-card'));
      expect(await productCards.count()).toBeGreaterThan(0);
    });
  });
});
