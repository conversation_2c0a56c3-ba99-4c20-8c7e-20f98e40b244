// Test setup file for Angular testing
import 'zone.js/testing';
import { getTestBed } from '@angular/core/testing';
import {
  BrowserDynamicTestingModule,
  platformBrowserDynamicTesting
} from '@angular/platform-browser-dynamic/testing';

// First, initialize the Angular testing environment.
getTestBed().initTestEnvironment(
  BrowserDynamicTestingModule,
  platformBrowserDynamicTesting(),
);

// Global test utilities
declare global {
  namespace jasmine {
    interface Matchers<T> {
      toBeVisible(): boolean;
      toHaveClass(className: string): boolean;
      toContainText(text: string): boolean;
    }
  }
}

// Custom matchers
beforeEach(() => {
  jasmine.addMatchers({
    toBeVisible: () => ({
      compare: (actual: any) => {
        const element = actual.nativeElement || actual;
        const isVisible = element && 
          element.offsetWidth > 0 && 
          element.offsetHeight > 0 && 
          window.getComputedStyle(element).visibility !== 'hidden';
        
        return {
          pass: isVisible,
          message: `Expected element to be visible`
        };
      }
    }),
    
    toHaveClass: () => ({
      compare: (actual: any, className: string) => {
        const element = actual.nativeElement || actual;
        const hasClass = element && element.classList.contains(className);
        
        return {
          pass: hasClass,
          message: `Expected element to have class '${className}'`
        };
      }
    }),
    
    toContainText: () => ({
      compare: (actual: any, text: string) => {
        const element = actual.nativeElement || actual;
        const containsText = element && element.textContent.includes(text);
        
        return {
          pass: containsText,
          message: `Expected element to contain text '${text}'`
        };
      }
    })
  });
});

// Global test configuration
beforeEach(() => {
  // Reset any global state
  localStorage.clear();
  sessionStorage.clear();
  
  // Mock console methods to reduce noise in tests
  spyOn(console, 'log').and.stub();
  spyOn(console, 'warn').and.stub();
  spyOn(console, 'error').and.stub();
});

// Mock IntersectionObserver for tests
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver for tests
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia for tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo for tests
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn()
});

// Mock fetch for tests
global.fetch = jest.fn();

// Test data factories
export const TestDataFactory = {
  createUser: (overrides = {}) => ({
    _id: 'user123',
    username: 'testuser',
    email: '<EMAIL>',
    fullName: 'Test User',
    role: 'buyer',
    avatar: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date(),
    ...overrides
  }),

  createProduct: (overrides = {}) => ({
    _id: 'product123',
    name: 'Test Product',
    description: 'A test product description',
    price: 99.99,
    originalPrice: 129.99,
    brand: 'Test Brand',
    category: 'Test Category',
    images: [
      { url: 'https://via.placeholder.com/300', alt: 'Test Product' }
    ],
    isActive: true,
    stock: 10,
    rating: 4.5,
    reviewCount: 25,
    isNew: false,
    isTrending: true,
    createdAt: new Date(),
    ...overrides
  }),

  createCartItem: (overrides = {}) => ({
    _id: 'cartitem123',
    product: TestDataFactory.createProduct(),
    quantity: 1,
    size: 'M',
    color: 'Blue',
    addedAt: new Date(),
    ...overrides
  }),

  createOrder: (overrides = {}) => ({
    _id: 'order123',
    orderNumber: 'ORD-2024-001',
    user: TestDataFactory.createUser(),
    items: [TestDataFactory.createCartItem()],
    status: 'pending',
    totalAmount: 99.99,
    shippingAddress: {
      street: '123 Test St',
      city: 'Test City',
      state: 'Test State',
      zipCode: '12345',
      country: 'Test Country'
    },
    createdAt: new Date(),
    ...overrides
  })
};

// Test utilities
export const TestUtils = {
  // Wait for async operations
  waitFor: (condition: () => boolean, timeout = 5000): Promise<void> => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        if (condition()) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          reject(new Error('Timeout waiting for condition'));
        } else {
          setTimeout(check, 100);
        }
      };
      check();
    });
  },

  // Simulate user input
  simulateInput: (element: HTMLInputElement, value: string) => {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
  },

  // Simulate click
  simulateClick: (element: HTMLElement) => {
    element.click();
    element.dispatchEvent(new Event('click', { bubbles: true }));
  },

  // Create mock HTTP response
  createMockResponse: (data: any, status = 200) => ({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data))
  }),

  // Create mock error response
  createMockError: (status: number, message: string) => ({
    ok: false,
    status,
    statusText: 'Error',
    json: () => Promise.resolve({ error: message }),
    text: () => Promise.resolve(JSON.stringify({ error: message }))
  })
};

// Performance testing utilities
export const PerformanceTestUtils = {
  measureTime: async (fn: () => Promise<any>): Promise<{ result: any, duration: number }> => {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    return { result, duration };
  },

  expectPerformance: (duration: number, maxDuration: number) => {
    expect(duration).toBeLessThan(maxDuration);
  }
};

// Accessibility testing utilities
export const A11yTestUtils = {
  checkAriaLabels: (element: HTMLElement) => {
    const interactiveElements = element.querySelectorAll(
      'button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    interactiveElements.forEach(el => {
      const hasLabel = el.getAttribute('aria-label') || 
                      el.getAttribute('aria-labelledby') ||
                      (el as HTMLInputElement).labels?.length > 0;
      
      expect(hasLabel).toBeTruthy(`Element ${el.tagName} should have accessible label`);
    });
  },

  checkKeyboardNavigation: (element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'button:not([disabled]), a[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    expect(focusableElements.length).toBeGreaterThan(0);
    
    focusableElements.forEach(el => {
      expect(el.getAttribute('tabindex')).not.toBe('-1');
    });
  },

  checkColorContrast: (element: HTMLElement) => {
    // This would require a color contrast library in a real implementation
    // For now, just check that text elements have color styles
    const textElements = element.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
    
    textElements.forEach(el => {
      const styles = window.getComputedStyle(el);
      expect(styles.color).toBeTruthy();
    });
  }
};

// Mock services for testing
export const MockServices = {
  createMockRouter: () => ({
    navigate: jasmine.createSpy('navigate').and.returnValue(Promise.resolve(true)),
    navigateByUrl: jasmine.createSpy('navigateByUrl').and.returnValue(Promise.resolve(true)),
    url: '/',
    events: {
      subscribe: jasmine.createSpy('subscribe')
    }
  }),

  createMockActivatedRoute: (params = {}, queryParams = {}) => ({
    params: { subscribe: (fn: any) => fn(params) },
    queryParams: { subscribe: (fn: any) => fn(queryParams) },
    snapshot: { params, queryParams }
  }),

  createMockHttpClient: () => ({
    get: jasmine.createSpy('get').and.returnValue(Promise.resolve({})),
    post: jasmine.createSpy('post').and.returnValue(Promise.resolve({})),
    put: jasmine.createSpy('put').and.returnValue(Promise.resolve({})),
    delete: jasmine.createSpy('delete').and.returnValue(Promise.resolve({}))
  })
};
